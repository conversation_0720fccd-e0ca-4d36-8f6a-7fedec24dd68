"""
Access control system for bot commands and messages.
Provides decorators and functions to check user permissions and group access.
"""

import functools
import time
from admin_system import is_user_blocked, is_admin
from database import is_group_unlocked
from rate_limiter import rate_limiter, format_time_remaining
from bot_globals import bot, log_admin


def check_access(require_admin=False, allow_private=True, allow_groups=True, check_rate_limit=True):
    """
    Decorator to check user access permissions.
    
    Args:
        require_admin: If True, only admins can use this command
        allow_private: If True, command works in private chats
        allow_groups: If True, command works in groups (if group is unlocked)
        check_rate_limit: If True, apply rate limiting (admins bypass)
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(message):
            user_id = message.from_user.id
            chat_id = message.chat.id
            is_private = chat_id == user_id
            is_group = message.chat.type in ["group", "supergroup"]
            
            # Check if user is blocked
            if is_user_blocked(user_id):
                return  # Silently ignore blocked users
            
            # Check admin requirement
            if require_admin and not is_admin(user_id):
                bot.reply_to(message, "❌ У вас нет прав администратора.")
                return
            
            # Check chat type permissions
            if is_private and not allow_private:
                bot.reply_to(message, "❌ Эта команда не работает в личных сообщениях.")
                return
            
            if is_group and not allow_groups:
                bot.reply_to(message, "❌ Эта команда не работает в группах.")
                return
            
            # Check group unlock status (only for groups)
            if is_group and not is_group_unlocked(chat_id):
                # Silently ignore - bot doesn't work in locked groups
                return
            
            # Check rate limiting (admins bypass)
            if check_rate_limit and not is_admin(user_id):
                allowed, should_warn, wait_time = rate_limiter.check_message_rate(user_id)
                
                if not allowed:
                    if should_warn:
                        wait_str = format_time_remaining(int(wait_time) + 1)
                        try:
                            bot.reply_to(message, f"⚠️ Слишком быстро! Подождите {wait_str}")
                        except:
                            pass
                    return
            
            # All checks passed, execute the function
            return func(message)
        
        return wrapper
    return decorator


def check_message_access(message):
    """
    Check if bot should process a message (for general message handler).
    Returns True if message should be processed, False otherwise.
    """
    user_id = message.from_user.id
    chat_id = message.chat.id
    is_private = chat_id == user_id
    is_group = message.chat.type in ["group", "supergroup"]
    
    # Check if user is blocked
    if is_user_blocked(user_id):
        return False
    
    # For private chats, always allow (unless user is blocked)
    if is_private:
        return True
    
    # For groups, check if group is unlocked
    if is_group:
        return is_group_unlocked(chat_id)
    
    # For other chat types, allow by default
    return True


def log_access_denied(user_id, chat_id, reason):
    """Log access denied events for debugging."""
    log_admin(f"Access denied for user {user_id} in chat {chat_id}: {reason}", level="debug")


def check_anonymous_callback_permission(call, original_user_id, original_chat_id, creation_timestamp, action="callback"):
    """
    Check if anonymous callback is allowed.

    Args:
        call: Telegram callback query object
        original_user_id: ID of user who created the original command
        original_chat_id: ID of chat where original command was created
        creation_timestamp: Timestamp when the original command was created
        action: Type of action for logging (e.g., "podcast_confirm", "podcast_cancel")

    Returns:
        tuple: (allowed: bool, reason: str)
    """
    try:
        current_user_id = call.from_user.id
        current_chat_id = call.message.chat.id
        current_time = time.time()

        log_admin(f"Anonymous callback check: current_user={current_user_id}, original_user={original_user_id}, "
                 f"current_chat={current_chat_id}, original_chat={original_chat_id}, "
                 f"time_diff={current_time - creation_timestamp:.1f}s", level="debug")

        # Case 1: Normal case - same user
        if current_user_id == original_user_id:
            log_admin(f"Anonymous callback allowed: same user {current_user_id}", level="debug")
            reason = "Same user"

            # Log the attempt
            try:
                from admin_system import log_anonymous_callback_attempt
                log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, True, reason)
            except:
                pass

            return True, reason

        # Special case: For podcast cancellation, only allow the original user
        if action == "podcast_cancel":
            log_admin(f"Anonymous callback denied: podcast cancellation only allowed for original user (current={current_user_id}, original={original_user_id})", level="debug")
            reason = "Only original user can cancel podcast"

            # Log the attempt
            try:
                from admin_system import log_anonymous_callback_attempt
                log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
            except:
                pass

            return False, reason

        # Case 2: Different chat - deny
        if current_chat_id != original_chat_id:
            log_admin(f"Anonymous callback denied: different chat (current={current_chat_id}, original={original_chat_id})", level="debug")
            reason = "Different chat"

            # Log the attempt
            try:
                from admin_system import log_anonymous_callback_attempt
                log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
            except:
                pass

            return False, reason

        # Case 3: Time limit check - 30 minutes (1800 seconds)
        time_limit = 30 * 60  # 30 minutes
        if current_time - creation_timestamp > time_limit:
            log_admin(f"Anonymous callback denied: expired (age={current_time - creation_timestamp:.1f}s)", level="debug")
            reason = "Expired button"

            # Log the attempt
            try:
                from admin_system import log_anonymous_callback_attempt
                log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
            except:
                pass

            return False, reason

        # Case 4: Check if original user is admin in the group/channel
        try:
            # Check if current chat is a group or supergroup
            if current_chat_id == original_user_id:  # Private chat
                log_admin(f"Anonymous callback denied: private chat, different user", level="debug")
                reason = "Private chat, different user"

                # Log the attempt
                try:
                    from admin_system import log_anonymous_callback_attempt
                    log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
                except:
                    pass

                return False, reason

            # For groups/supergroups, check if original user is admin
            if call.message.chat.type in ["group", "supergroup"]:
                try:
                    member = bot.get_chat_member(current_chat_id, original_user_id)
                    if member.status in ['administrator', 'creator']:
                        log_admin(f"Anonymous callback allowed: user {original_user_id} is admin in chat {current_chat_id}", level="debug")
                        reason = "Original user is admin"

                        # Log the attempt
                        try:
                            from admin_system import log_anonymous_callback_attempt
                            log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, True, reason)
                        except:
                            pass

                        return True, reason
                    else:
                        log_admin(f"Anonymous callback denied: user {original_user_id} is not admin in chat {current_chat_id} (status: {member.status})", level="debug")
                        reason = "Original user is not admin"

                        # Log the attempt
                        try:
                            from admin_system import log_anonymous_callback_attempt
                            log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
                        except:
                            pass

                        return False, reason
                except Exception as e:
                    log_admin(f"Anonymous callback denied: failed to check admin status for user {original_user_id} in chat {current_chat_id}: {e}", level="debug")
                    reason = "Failed to check admin status"

                    # Log the attempt
                    try:
                        from admin_system import log_anonymous_callback_attempt
                        log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
                    except:
                        pass

                    return False, reason

            # For channels, check if original user can post as channel
            elif call.message.chat.type == "channel":
                try:
                    member = bot.get_chat_member(current_chat_id, original_user_id)
                    if member.status in ['administrator', 'creator']:
                        log_admin(f"Anonymous callback allowed: user {original_user_id} is admin in channel {current_chat_id}", level="debug")
                        reason = "Original user is channel admin"

                        # Log the attempt
                        try:
                            from admin_system import log_anonymous_callback_attempt
                            log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, True, reason)
                        except:
                            pass

                        return True, reason
                    else:
                        log_admin(f"Anonymous callback denied: user {original_user_id} is not admin in channel {current_chat_id}", level="debug")
                        reason = "Original user is not channel admin"

                        # Log the attempt
                        try:
                            from admin_system import log_anonymous_callback_attempt
                            log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
                        except:
                            pass

                        return False, reason
                except Exception as e:
                    log_admin(f"Anonymous callback denied: failed to check channel admin status for user {original_user_id} in chat {current_chat_id}: {e}", level="debug")
                    reason = "Failed to check channel admin status"

                    # Log the attempt
                    try:
                        from admin_system import log_anonymous_callback_attempt
                        log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
                    except:
                        pass

                    return False, reason

            # Unknown chat type
            log_admin(f"Anonymous callback denied: unknown chat type {call.message.chat.type}", level="debug")
            reason = "Unknown chat type"

            # Log the attempt
            try:
                from admin_system import log_anonymous_callback_attempt
                log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
            except:
                pass

            return False, reason

        except Exception as e:
            log_admin(f"Anonymous callback denied: error checking permissions: {e}", level="error")
            reason = "Permission check error"

            # Log the attempt
            try:
                from admin_system import log_anonymous_callback_attempt
                log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
            except:
                pass

            return False, reason

    except Exception as e:
        log_admin(f"Anonymous callback denied: unexpected error: {e}", level="error")
        reason = "Unexpected error"

        # Log the attempt
        try:
            from admin_system import log_anonymous_callback_attempt
            log_anonymous_callback_attempt(current_user_id, original_user_id, current_chat_id, action, False, reason)
        except:
            pass

        return False, reason
