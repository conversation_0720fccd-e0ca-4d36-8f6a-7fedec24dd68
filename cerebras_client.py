"""
Модуль для работы с Cerebras API (Llama 4 Maverick).
Используется для определения сложности запросов пользователей.
"""

import requests
import json
import time
import random
from typing import Optional, Dict, Any
from bot_globals import log_admin

# API ключи и настройки для Cerebras
CEREBRAS_API_KEYS = [
    "csk-2rt6htct4nvwmpd6234kyym23d6pc6h5jfh533ykc38n6jed",  # основной
    "csk-xjcxn3c93d8xj9jj5pxf6wxt4hx4v38rj4852pfkefmn6f5c",
    "csk-y8n8djymknet3tehpr5r26cjt9x5ev68yetpndkjcr939hyp",
    "csk-xetknxkp2yj3y52w9vj3drdrrxrkvrdfh6hnvwpyev6jynek"
]
CEREBRAS_API_KEY = CEREBRAS_API_KEYS[0]  # Для обратной совместимости
CEREBRAS_API_URL = "https://api.cerebras.ai/v1/chat/completions"
CEREBRAS_MODEL = "llama-4-maverick-17b-128e-instruct"
CEREBRAS_SUMMARIZE_MODEL = "qwen-3-235b-a22b"

# Индекс текущего API ключа для авторотации
current_api_key_index = 0

# Системный промпт для определения сложности запроса
COMPLEXITY_SYSTEM_PROMPT = """Ты эксперт по анализу сложности текстовых запросов. Твоя задача - определить сложность запроса пользователя и ответить СТРОГО только одним тегом.

ПРАВИЛА КЛАССИФИКАЦИИ:
- [LITE] - только для самых простых вопросов: приветствия, базовые факты, простая арифметика, переводы отдельных слов
- [HARD] - для обычных сложных задач: анализ, программирование, объяснения, творческие задачи, специализированные знания
- [ULTRAPRO] - для задач требующих ГЛУБОКОГО размышления: философия, этика, сложные научные концепции, многофакторный анализ, теоретические модели, парадоксы, междисциплинарные вопросы

КРИТЕРИИ ДЛЯ [ULTRAPRO] (выбирай чаще):
- Философские вопросы любой сложности
- Этические дилеммы и моральные вопросы
- Научные теории и концепции (квантовая физика, космология, нейронауки)
- Анализ сложных социальных/политических/экономических явлений
- Вопросы о сознании, разуме, искусственном интеллекте
- Парадоксы и противоречия
- Междисциплинарные задачи
- Глобальные проблемы человечества
- Футурология и прогнозирование
- Сложные творческие задачи с множеством ограничений

ПРИМЕРЫ [LITE]:
- "Привет, как дела?"
- "Сколько будет 2+2?"
- "Переведи слово hello"

ПРИМЕРЫ [HARD]:
- "Напиши код на Python"
- "Объясни как работает интернет"
- "Создай план тренировок"

ПРИМЕРЫ [ULTRAPRO]:
- "Что такое сознание?"
- "Как решить проблему изменения климата?"
- "Объясни квантовую запутанность"
- "Этично ли создавать ИИ?"
- "Как будет выглядеть будущее человечества?"
- "В чем смысл жизни?"
- "Как работает время?"
- "Что такое справедливость?"
- "Возможно ли бессмертие?"
- "Как возникла вселенная?"

ВАЖНО: При сомнении между HARD и ULTRAPRO - выбирай ULTRAPRO! Отвечай ТОЛЬКО тегом [LITE], [HARD] или [ULTRAPRO]. Никакого другого текста!"""

# Системный промпт для сокращения текста
SUMMARIZE_SYSTEM_PROMPT = """Сократи предоставленный текст до 200 символов, чтоб вся суть была ОЧЕНЬ КРАТКО, сохранив только ключевые моменты и важную информацию. Используй простой язык без форматирования, списков или заголовков. Отвечай ТОЛЬКО сокращенным текстом без комментариев."""


class CerebrasClient:
    """Клиент для работы с Cerebras API."""
    
    def __init__(self):
        self.api_key = CEREBRAS_API_KEY
        self.api_url = CEREBRAS_API_URL
        self.model = CEREBRAS_MODEL
        self.timeout = 10  # Быстрый таймаут для определения сложности
        
    def classify_complexity(self, user_query: str) -> str:
        """
        Определяет сложность запроса пользователя.
        
        Args:
            user_query: Запрос пользователя
            
        Returns:
            "LITE", "HARD" или "ULTRAPRO" в зависимости от сложности
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": COMPLEXITY_SYSTEM_PROMPT
                    },
                    {
                        "role": "user", 
                        "content": user_query
                    }
                ],
                "max_tokens": 10,  # Очень мало токенов, нужен только тег
                "temperature": 0.1,  # Низкая температура для стабильности
                "stream": False
            }
            
            start_time = time.time()
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
                
                # Парсим ответ
                if "[ULTRAPRO]" in content.upper():
                    complexity = "ULTRAPRO"
                elif "[HARD]" in content.upper():
                    complexity = "HARD"
                elif "[LITE]" in content.upper():
                    complexity = "LITE"
                else:
                    # Fallback - если не удалось распознать, считаем сложным
                    complexity = "HARD"
                    log_admin(f"[Cerebras] Unexpected response format: '{content}', defaulting to HARD", level="warning")
                
                log_admin(f"[Cerebras] Query classified as {complexity} in {duration:.2f}s", level="debug")
                return complexity
                
            else:
                log_admin(f"[Cerebras] API error {response.status_code}: {response.text}", level="error")
                return "HARD"  # Fallback к сложному при ошибке
                
        except requests.exceptions.Timeout:
            log_admin("[Cerebras] Request timeout, defaulting to HARD", level="warning")
            return "HARD"
        except Exception as e:
            log_admin(f"[Cerebras] Error classifying complexity: {e}", level="error")
            return "HARD"  # Fallback к сложному при ошибке


# Глобальный экземпляр клиента
cerebras_client = CerebrasClient()


def classify_query_complexity(user_query: str) -> str:
    """
    Определяет сложность запроса пользователя через Llama 4 Maverick.

    Args:
        user_query: Запрос пользователя

    Returns:
        "LITE", "HARD" или "ULTRAPRO"
    """
    return cerebras_client.classify_complexity(user_query)


def shorten_ai_response(text: str) -> Optional[str]:
    """
    Сокращает ответ ИИ с помощью Llama 4 Maverick через Cerebras API.
    
    Args:
        text: Исходный текст для сокращения
        
    Returns:
        Сокращенный текст или None в случае ошибки
    """
    if not text or not text.strip():
        return None
        
    # Системный промпт для сокращения
    system_prompt = """Ты - эксперт по сокращению текстов. Твоя задача - максимально сократить данный текст, сохранив все ключевые идеи и факты.

ПРАВИЛА:
- Убери все лишние слова и повторы
- Сохрани все важные факты и цифры
- Используй простые предложения
- НЕ используй форматирование (жирный текст, курсив, заголовки)
- НЕ добавляй новую информацию
- Сократи как минимум на 50% от исходной длины
- Пиши сжато и по существу

Верни только сокращенный текст без комментариев."""

    try:
        # Используем прямой API вызов как в classify_complexity
        headers = {
            "Authorization": f"Bearer {CEREBRAS_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": CEREBRAS_MODEL,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Сократи этот текст:\n\n{text}"}
            ],
            "max_tokens": min(2000, len(text) // 2),  # Ограничиваем длину ответа
            "temperature": 0.3,  # Низкая температура для стабильности
            "stream": False
        }
        
        start_time = time.time()
        response = requests.post(
            CEREBRAS_API_URL,
            headers=headers,
            json=payload,
            timeout=30  # Больше времени для сокращения длинных текстов
        )
        duration = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            shortened = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            
            if shortened and len(shortened) < len(text):
                log_admin(f"Text shortened: {len(text)} -> {len(shortened)} chars in {duration:.2f}s", level="debug")
                return shortened
            else:
                log_admin(f"Text shortening failed: result not shorter than original", level="warning")
                return None
        else:
            log_admin(f"Cerebras API error {response.status_code}: {response.text}", level="error")
            return None
            
    except requests.exceptions.Timeout:
        log_admin("Cerebras API timeout during text shortening", level="error")
        return None
    except Exception as e:
        log_admin(f"Error shortening AI response: {e}", level="error")
        return None


def remove_think_tags(text: str) -> str:
    """
    Удаляет теги <think>...</think> вместе с содержимым из текста.

    Args:
        text: Исходный текст

    Returns:
        Текст без тегов <think> и их содержимого
    """
    import re
    # Удаляем все блоки <think>...</think> вместе с содержимым
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.IGNORECASE | re.DOTALL)
    # Удаляем одиночные теги </think> если остались
    text = re.sub(r'</think>', '', text, flags=re.IGNORECASE)
    return text.strip()


def summarize_text(text: str) -> Optional[str]:
    """
    Сокращает длинный текст через Cerebras API с авторотацией ключей.

    Args:
        text: Текст для сокращения

    Returns:
        Сокращенный текст или None при ошибке
    """
    global current_api_key_index

    if not text or len(text.strip()) == 0:
        return None

    # Пробуем все API ключи
    for attempt in range(len(CEREBRAS_API_KEYS)):
        api_key = CEREBRAS_API_KEYS[current_api_key_index]

        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": CEREBRAS_SUMMARIZE_MODEL,
                "messages": [
                    {
                        "role": "system",
                        "content": SUMMARIZE_SYSTEM_PROMPT
                    },
                    {
                        "role": "user",
                        "content": f"Сократи этот текст:\n\n{text}"
                    }
                ],
                "max_tokens": 2048,
                "temperature": 0.3,
                "stream": False
            }

            start_time = time.time()
            response = requests.post(
                CEREBRAS_API_URL,
                headers=headers,
                json=payload,
                timeout=30
            )
            duration = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()

                if content:
                    # Удаляем теги </think>
                    content = remove_think_tags(content)
                    log_admin(f"[Cerebras Summarize] Text summarized successfully in {duration:.2f}s with key index {current_api_key_index}", level="debug")
                    return content
                else:
                    log_admin(f"[Cerebras Summarize] Empty response from API", level="warning")
                    return None

            elif response.status_code == 429:
                # Rate limit - переключаемся на следующий ключ
                log_admin(f"[Cerebras Summarize] Rate limit hit with key index {current_api_key_index}, switching to next key", level="warning")
                current_api_key_index = (current_api_key_index + 1) % len(CEREBRAS_API_KEYS)
                continue

            else:
                log_admin(f"[Cerebras Summarize] API error {response.status_code} with key index {current_api_key_index}: {response.text}", level="error")
                current_api_key_index = (current_api_key_index + 1) % len(CEREBRAS_API_KEYS)
                continue

        except requests.exceptions.Timeout:
            log_admin(f"[Cerebras Summarize] Request timeout with key index {current_api_key_index}", level="warning")
            current_api_key_index = (current_api_key_index + 1) % len(CEREBRAS_API_KEYS)
            continue
        except Exception as e:
            log_admin(f"[Cerebras Summarize] Error with key index {current_api_key_index}: {e}", level="error")
            current_api_key_index = (current_api_key_index + 1) % len(CEREBRAS_API_KEYS)
            continue

    # Если все ключи не сработали
    log_admin("[Cerebras Summarize] All API keys failed", level="error")
    return None