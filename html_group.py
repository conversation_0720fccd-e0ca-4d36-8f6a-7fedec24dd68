"""
Модуль для генерации HTML сайтов в группах.
Использует Gemini 2.5 Pro (приоритет) и Cerebras API для генерации HTML.

ОСОБЕННОСТИ:
- Приоритетное использование Gemini 2.5 Pro (без ограничений по времени)
- Fallback на Cerebras API (без ограничений по времени)
- Простая генерация HTML файлов
- Работает без дополнительных зависимостей
- Быстрая обработка запросов
- Полностью неограниченное количество одновременных запросов
- Истинно параллельное выполнение через thread pool
"""

import re
import requests
import json
import time
import threading
import asyncio
import aiohttp
import random
import base64
import os
from typing import Tuple, Dict
from bot_globals import bot, log_admin
from config import OFFICIAL_GEMINI_API_KEYS


# === КОНФИГУРАЦИЯ CEREBRAS API ===
CEREBRAS_API_KEYS = [
    "csk-2rt6htct4nvwmpd6234kyym23d6pc6h5jfh533ykc38n6jed",  # основной
    "csk-xjcxn3c93d8xj9jj5pxf6wxt4hx4v38rj4852pfkefmn6f5c",
    "csk-y8n8djymknet3tehpr5r26cjt9x5ev68yetpndkjcr939hyp"
]

CEREBRAS_API_URL = "https://api.cerebras.ai/v1/chat/completions"
CEREBRAS_MODEL = "qwen-3-235b-a22b"

# Параметры API
API_PARAMS = {
    "max_tokens": 8192,
    "temperature": 0.12,
    "top_p": 0.95
}

# Системный промпт для улучшения пользовательских промптов
PROMPT_IMPROVEMENT_SYSTEM = """
Ты — эксперт по улучшению промптов для создания HTML-сайтов. Твоя задача — взять пользовательский запрос и сделать его максимально детальным, конкретным и техническим для точной реализации.

ВАЖНЫЕ ПРИНЦИПЫ:
1) HTML ЭТО НЕ ВСЕГДА ЛЕНДИНГ! Если пользователь просит 3D модель — делай ЧИСТО 3D МОДЕЛЬ БЕЗ ЛИШНИХ КНОПОК И ЭЛЕМЕНТОВ ИНТЕРФЕЙСА. Только самое нужное.
2) Если просят игру — делай игру, а не лендинг об игре.
3) Если просят калькулятор — делай калькулятор, а не сайт о калькуляторах.
4) Если просят визуализацию данных — делай визуализацию, а не описание.

ТВОЯ ЗАДАЧА:
- Развить идею пользователя в детальное техническое описание
- Указать конкретные элементы интерфейса, которые нужны
- Описать функциональность и поведение
- Уточнить стили и внешний вид
- Добавить технические детали реализации

ФОРМАТ ОТВЕТА:
Верни только улучшенный промпт без пояснений, комментариев или дополнительного текста.

Пример:
Вход: "создай 3д куб"
Выход: "Создай интерактивную 3D модель вращающегося куба с использованием CSS 3D transforms. Куб должен иметь разные цвета граней (красный, синий, зеленый, желтый, фиолетовый, оранжевый), плавно вращаться по осям X и Y с помощью CSS анимации. Добавь возможность управления вращением мышью - при наведении анимация замедляется, при клике меняется направление вращения. Куб должен быть по центру экрана на темном фоне."
"""

# Системный промпт (жесткие требования: только запрошенное, без заглушек, полностью рабочий HTML)
SYSTEM_PROMPT = """
СОЗДАЙ ОДИН-ЕДИНСТВЕННЫЙ ПОЛНОСТЬЮ РАБОТАЮЩИЙ HTML-ДОКУМЕНТ СТРОГО ПО ЗАПРОСУ ПОЛЬЗОВАТЕЛЯ.

Жесткие требования:
1) Верни только валидный HTML-код от <!DOCTYPE html> до </html>. Никаких пояснений, комментариев вне кода, markdown-блоков, пустых строк до/после.
2) НИЧЕГО ЛИШНЕГО: не добавляй кнопки, формы, ссылки, секции, модальные окна, карусели, анимации и любые другие элементы, если пользователь их прямо не запросил. Не «украшай» интерфейс по своей инициативе.
3) НИКАКИХ ЗАГЛУШЕК: запрещены неработающие кнопки, пустые обработчики, комментарии вида "TODO", "здесь будет логика", мертвые ссылки, пустые href="#" без реального назначения (кроме случаев, когда пользователь явно попросил заглушку).
4) ВСЕ ФУНКЦИИ ДОЛЖНЫ РАБОТАТЬ: если пользователь просит интерактив — реализуй рабочую логику на чистом JS в одном <script type="module"> без внешних зависимостей. Не оставляй незавершенных обработчиков. Если требуются данные — либо используй те, что предоставлены пользователем, либо создай локальные реалистичные тестовые данные.
5) АВТОНОМНОСТЬ: сайт должен открываться и корректно работать локально в браузере сразу после вставки ответа в файл. Интернет-ресурсы допускаются только если пользователь прямо их запросил (например, конкретный CDN или внешний API). Иначе — без внешних подключений.
6) СТРУКТУРА КОДА:
   - Один HTML документ.
   - Стили внутри одного <style> блока.
   - Скрипт внутри одного <script type="module"> блока.
   - Не использовать лишних <link>, <script>, <iframe>, <img> и т.п., если это не указано в запросе.
7) ВАЛИДНОСТЬ: соблюдай семантику HTML5, отсутствие ошибок в консоли, отсутствие необъявленных переменных/функций, корректная работа всех обработчиков событий.
8) ДАННЫЕ И ТЕКСТЫ: используй формулировки и тексты из запроса. Не добавляй фиктивный контент сверх требуемого. Если нужны примеры — делай их минимальными и уместными.
9) ФОРМАТ ОТВЕТА: верни единственный HTML-документ, соответствующий всем правилам. Никаких пояснений, кодовых ограждений ``` и прочего окружения.

Следуй только явным требованиям пользователя. Если что-то не запрошено — не добавляй.
"""

# Текущий индекс API ключа
current_api_key_index = 0

# === КОНФИГУРАЦИЯ OPENROUTER API ДЛЯ /html ===
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"
OPENROUTER_MODEL = "openrouter/horizon-alpha"
# ВСТРОЕННЫЙ КЛЮЧ ПО ПРОСЬБЕ ПОЛЬЗОВАТЕЛЯ
OPENROUTER_API_KEY = "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"

# Оставляем старые константы Gemini, но не используем их в /html
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent"
GEMINI_MODEL = "gemini-2.5-pro"

# === СИСТЕМА ОГРАНИЧЕНИЙ ===

# Словарь для отслеживания времени последних запросов пользователей
# Формат: {user_id: timestamp_last_request}
user_last_request_time: Dict[int, float] = {}

# Словарь для отслеживания времени последних OpenRouter-запросов пользователей (для группы /html)
# Формат: {user_id: timestamp_last_request}
gemini_user_last_request_time: Dict[int, float] = {}

# Полное отсутствие ограничений: неограниченное количество одновременных запросов
RATE_LIMIT_SECONDS = 0

# Полное отсутствие ограничений OpenRouter: неограниченное количество одновременных запросов
GEMINI_RATE_LIMIT_SECONDS = 0

# Время автоудаления предупреждающих сообщений (в секундах)
WARNING_MESSAGE_DELETE_DELAY = 10


# === УТИЛИТЫ ===

async def animate_progress_bar(chat_id: int, message_id: int, duration: float = 4.0, user_id: int = None):
    """
    Асинхронная анимация прогресс-бара от 0% до 100% за указанное время.
    Обновляется каждые 0.4 секунды.

    Args:
        chat_id: ID чата для обновления сообщения
        message_id: ID сообщения для обновления
        duration: Длительность анимации в секундах (по умолчанию 4 секунды)
        user_id: ID пользователя для показа времени до Gemini (опционально)
    """
    # Символы для прогресс-бара
    empty_char = "░"
    filled_char = "█"
    bar_length = 20

    # Частота обновления (обновляем каждые 0.4 секунды)
    update_interval = 0.9
    total_updates = int(duration / update_interval)

    try:
        for i in range(total_updates + 1):
            # Вычисляем прогресс
            progress = min(100, (i / total_updates) * 100)
            filled_length = int((progress / 100) * bar_length)

            # Создаем визуальный прогресс-бар
            bar = filled_char * filled_length + empty_char * (bar_length - filled_length)

            # Форматируем сообщение
            status_text = f"🥵 Генерирую HTML сайт...\n\n{bar} {progress:.1f}%"

            # Добавляем информацию о времени до Gemini, если указан user_id
            if user_id is not None:
                can_use_gemini, gemini_seconds_remaining = check_gemini_rate_limit(user_id)
                if not can_use_gemini and gemini_seconds_remaining > 0:
                    gemini_minutes_remaining = int(gemini_seconds_remaining // 60)
                    gemini_seconds_remaining = int(gemini_seconds_remaining % 60)

                    if gemini_minutes_remaining > 0:
                        gemini_time_text = f"{gemini_minutes_remaining} мин {gemini_seconds_remaining} сек"
                    else:
                        gemini_time_text = f"{gemini_seconds_remaining} сек"

                    status_text += f"\n⏰ Следующий Gemini через: {gemini_time_text}"

            # Обновляем сообщение
            try:
                bot.edit_message_text(
                    status_text,
                    chat_id=chat_id,
                    message_id=message_id
                )
            except Exception as e:
                # Игнорируем ошибки редактирования (например, если сообщение не изменилось)
                if "message is not modified" not in str(e).lower():
                    log_admin(f"HTML Group: Progress bar update error: {e}", level="debug")

            # Ждем до следующего обновления
            if i < total_updates:
                await asyncio.sleep(update_interval)

    except Exception as e:
        log_admin(f"HTML Group: Progress bar animation error: {e}", level="error")





def delete_message_after_delay(chat_id: int, message_id: int, delay_seconds: int):
    """
    Удаляет сообщение через указанное количество секунд.
    Запускается в отдельном потоке.
    """
    def delete_message():
        try:
            time.sleep(delay_seconds)
            bot.delete_message(chat_id, message_id)
            log_admin(f"HTML Group: Auto-deleted message {message_id} in chat {chat_id}", level="debug")
        except Exception as e:
            log_admin(f"HTML Group: Failed to auto-delete message {message_id}: {e}", level="warning")

    # Запускаем удаление в отдельном потоке
    thread = threading.Thread(target=delete_message, daemon=True)
    thread.start()








def check_rate_limit(user_id: int) -> Tuple[bool, float]:
    """
    Проверяет, может ли пользователь создать новый сайт.

    Args:
        user_id: ID пользователя

    Returns:
        Tuple[bool, float]: (can_create, seconds_remaining)
        - can_create: True если пользователь может создать сайт
        - seconds_remaining: сколько секунд осталось до следующего разрешенного запроса
    """
    current_time = time.time()

    if user_id not in user_last_request_time:
        return True, 0.0

    time_since_last_request = current_time - user_last_request_time[user_id]

    if time_since_last_request >= RATE_LIMIT_SECONDS:
        return True, 0.0
    else:
        seconds_remaining = RATE_LIMIT_SECONDS - time_since_last_request
        return False, seconds_remaining


def update_user_request_time(user_id: int):
    """
    Обновляет время последнего запроса пользователя.
    """
    user_last_request_time[user_id] = time.time()


def check_gemini_rate_limit(user_id: int) -> Tuple[bool, float]:
    """
    Проверяет, может ли пользователь создать новый сайт через Gemini.

    Args:
        user_id: ID пользователя

    Returns:
        Tuple[bool, float]: (can_create, seconds_remaining)
        - can_create: True если пользователь может использовать Gemini
        - seconds_remaining: сколько секунд осталось до следующего разрешенного Gemini запроса
    """
    current_time = time.time()

    if user_id not in gemini_user_last_request_time:
        return True, 0.0

    time_since_last_request = current_time - gemini_user_last_request_time[user_id]

    if time_since_last_request >= GEMINI_RATE_LIMIT_SECONDS:
        return True, 0.0
    else:
        seconds_remaining = GEMINI_RATE_LIMIT_SECONDS - time_since_last_request
        return False, seconds_remaining


def update_gemini_request_time(user_id: int):
    """
    Обновляет время последнего Gemini запроса пользователя.
    """
    gemini_user_last_request_time[user_id] = time.time()

def clean_html_response(response_text: str) -> str:
    """
    Очищает ответ от тегов <think>...</think>, markdown-символов и других мусорных тегов.
    Возвращает только чистый HTML код.
    """
    if not response_text:
        return ""

    # Удаляем теги <think>...</think> (могут быть многострочными)
    cleaned = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL | re.IGNORECASE)

    # Удаляем другие возможные мусорные теги
    cleaned = re.sub(r'<reasoning>.*?</reasoning>', '', cleaned, flags=re.DOTALL | re.IGNORECASE)
    cleaned = re.sub(r'<analysis>.*?</analysis>', '', cleaned, flags=re.DOTALL | re.IGNORECASE)

    # Удаляем markdown-символы ``` в начале и конце
    cleaned = re.sub(r'^```[a-zA-Z]*\s*', '', cleaned, flags=re.MULTILINE)  # ``` в начале строк
    cleaned = re.sub(r'\s*```\s*$', '', cleaned, flags=re.MULTILINE)       # ``` в конце строк
    cleaned = re.sub(r'```', '', cleaned)  # Любые оставшиеся ```

    # Убираем лишние пробелы и переносы строк в начале и конце
    cleaned = cleaned.strip()

    # Проверяем, что результат начинается с <!DOCTYPE html> или <html>
    if not (cleaned.lower().startswith('<!doctype html') or cleaned.lower().startswith('<html')):
        # Если нет, ищем первое вхождение <!DOCTYPE html> или <html>
        doctype_match = re.search(r'<!doctype html.*?>', cleaned, re.IGNORECASE | re.DOTALL)
        html_match = re.search(r'<html.*?>', cleaned, re.IGNORECASE)

        if doctype_match:
            cleaned = cleaned[doctype_match.start():]
        elif html_match:
            cleaned = cleaned[html_match.start():]

    # Финальная очистка от лишних пробелов и переносов строк
    cleaned = cleaned.strip()

    return cleaned


def generate_filename_from_prompt(prompt: str) -> str:
    """
    Генерирует имя файла из первых 3 слов промпта.
    Возвращает имя файла в формате "word1_word2_word3.html"
    """
    if not prompt:
        return "generated_site.html"

    # Убираем команду /html если она есть
    if prompt.lower().startswith('/html'):
        prompt = prompt[5:].strip()

    # Разбиваем на слова и берем первые 3
    words = re.findall(r'\b\w+\b', prompt.lower())

    if not words:
        return "generated_site.html"

    # Берем первые 3 слова (или меньше, если слов меньше 3)
    filename_words = words[:3]

    # Создаем имя файла
    filename = "_".join(filename_words) + ".html"

    # Убираем недопустимые символы для имени файла
    filename = re.sub(r'[^\w\-_\.]', '', filename)

    return filename


# === УТИЛИТАРНЫЕ ФУНКЦИИ ДЛЯ ПЛАШКИ MEAN ===

def get_user_display_name(message) -> str:
    """
    Получает отображаемое имя пользователя с приоритетом:
    1. @username (если есть)
    2. "Имя Фамилия" (если есть имя)
    3. "Имя" (если есть только имя)
    4. "Пользователь" (fallback)

    Args:
        message: Объект сообщения Telegram

    Returns:
        str: Отображаемое имя пользователя
    """
    user = message.from_user

    # Приоритет 1: username
    if user.username:
        return f"@{user.username}"

    # Приоритет 2: имя + фамилия
    if user.first_name and user.last_name:
        return f"{user.first_name} {user.last_name}"

    # Приоритет 3: только имя
    if user.first_name:
        return user.first_name

    # Fallback
    return "Пользователь"


def create_message_link(chat_id: int, message_id: int) -> str:
    """
    Создает ссылку на сообщение в группе.
    Обрабатывает отрицательные chat_id для групп.

    Args:
        chat_id: ID чата (может быть отрицательным для групп)
        message_id: ID сообщения

    Returns:
        str: Ссылка на сообщение в формате https://t.me/c/{processed_chat_id}/{message_id}
    """
    # Обрабатываем отрицательный chat_id
    if chat_id < 0:
        # Для супергрупп (-100...) убираем минус и первые 3 цифры (100)
        if str(chat_id).startswith('-100'):
            processed_chat_id = str(abs(chat_id))[3:]
        else:
            # Для обычных групп (-...) убираем только минус
            processed_chat_id = str(abs(chat_id))
    else:
        # Для положительных ID (хотя в группах такого быть не должно)
        processed_chat_id = str(chat_id)

    return f"https://t.me/c/{processed_chat_id}/{message_id}"


def truncate_prompt(prompt: str, max_words: int = 8) -> str:
    """
    Обрезает промпт если он содержит больше указанного количества слов.
    Добавляет "..." в конце при обрезке.

    Args:
        prompt: Исходный промпт
        max_words: Максимальное количество слов (по умолчанию 8)

    Returns:
        str: Обрезанная версия промпта для отображения
    """
    if not prompt:
        return ""

    # Разбиваем на слова
    words = prompt.split()

    # Если слов меньше или равно максимуму, возвращаем как есть
    if len(words) <= max_words:
        return prompt

    # Обрезаем до максимального количества слов и добавляем "..."
    truncated_words = words[:max_words]
    return " ".join(truncated_words) + "..."


def generate_overlay_html(user_name: str, prompt_display: str, prompt_full: str, message_link: str) -> str:
    """
    Генерирует HTML код overlay с плашкой MEAN поверх сайта.
    Адаптирует стили из MEAN.html для overlay системы.

    Args:
        user_name: Отображаемое имя пользователя
        prompt_display: Обрезанная версия промпта для отображения
        prompt_full: Полный промпт для title атрибута
        message_link: Ссылка на исходное сообщение

    Returns:
        str: HTML код overlay с CSS и JavaScript
    """
    import html

    # Экранируем HTML для безопасности
    safe_user_name = html.escape(user_name)
    safe_prompt_display = html.escape(prompt_display)
    safe_prompt_full = html.escape(prompt_full)
    safe_message_link = html.escape(message_link)

    overlay_html = f"""
<!-- MEAN Overlay Start -->
<div id="mean-overlay-container" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Press Start 2P', cursive;
">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">

    <div class="mean-overlay-prompt-container" style="
        width: 550px;
        height: 550px;
        max-width: 90vw;
        max-height: 90vh;
        background-color: #282828;
        border: 8px solid #FFD700;
        box-shadow: 10px 10px 0px #a18700;
        padding: 30px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        color: #E0E0E0;
        line-height: 1.8;
    ">
        <!-- Верхний блок: заголовок и промпт -->
        <div>
            <p class="mean-overlay-prompt-header" style="
                font-size: 1.2em;
                color: #FFD700;
                text-align: left;
                margin: 0;
            ">Промпт:</p>
            <p class="mean-overlay-prompt-body" style="
                font-size: 1em;
                text-align: center;
                white-space: pre-wrap;
                word-break: break-word;
                margin: 20px 0;
            ">
                <a href="{safe_message_link}" target="_blank" title="{safe_prompt_full}" style="
                    color: #E0E0E0;
                    text-decoration: none;
                    cursor: pointer;
                ">{safe_prompt_display}</a>
            </p>
        </div>

        <!-- Нижний блок: подпись -->
        <div>
            <p class="mean-overlay-footer-text" style="
                text-align: right;
                font-size: 0.9em;
                color: #999;
                margin: 0;
            ">
                по просьбе {safe_user_name}<br>
                by <a href="https://t.me/UseShBot" target="_blank" style="
                    color: #FFD700;
                    text-decoration: none;
                ">@UseShBot</a>
            </p>
        </div>
    </div>
</div>

<script>
(function() {{
    // Автоматическое скрытие overlay через 2 секунды
    setTimeout(function() {{
        var overlay = document.getElementById('mean-overlay-container');
        if (overlay) {{
            // Анимация fade-out
            overlay.style.transition = 'opacity 0.5s ease-out';
            overlay.style.opacity = '0';

            // Удаление из DOM после анимации
            setTimeout(function() {{
                if (overlay.parentNode) {{
                    overlay.parentNode.removeChild(overlay);
                }}
            }}, 500);
        }}
    }}, 2000);

    // Возможность закрыть overlay кликом по фону
    var overlay = document.getElementById('mean-overlay-container');
    if (overlay) {{
        overlay.addEventListener('click', function(e) {{
            if (e.target === overlay) {{
                overlay.style.transition = 'opacity 0.3s ease-out';
                overlay.style.opacity = '0';
                setTimeout(function() {{
                    if (overlay.parentNode) {{
                        overlay.parentNode.removeChild(overlay);
                    }}
                }}, 300);
            }}
        }});
    }}
}})();
</script>
<!-- MEAN Overlay End -->
"""

    return overlay_html


def inject_overlay_into_html(original_html: str, overlay_html: str) -> str:
    """
    Встраивает overlay HTML в оригинальный HTML.
    Ищет тег <body> и добавляет overlay в начало.
    Обрабатывает случаи когда <body> не найден (fallback).

    Args:
        original_html: Оригинальный HTML код
        overlay_html: HTML код overlay для встраивания

    Returns:
        str: Модифицированный HTML с встроенным overlay
    """
    import re

    # Попытка 1: Найти тег <body> (с возможными атрибутами)
    body_pattern = r'(<body[^>]*>)'
    body_match = re.search(body_pattern, original_html, re.IGNORECASE)

    if body_match:
        # Найден тег <body>, вставляем overlay сразу после него
        body_tag = body_match.group(1)
        insertion_point = body_match.end()

        modified_html = (
            original_html[:insertion_point] +
            "\n" + overlay_html + "\n" +
            original_html[insertion_point:]
        )
        return modified_html

    # Попытка 2: Найти тег <html> (fallback)
    html_pattern = r'(<html[^>]*>)'
    html_match = re.search(html_pattern, original_html, re.IGNORECASE)

    if html_match:
        # Найден тег <html>, вставляем overlay сразу после него
        insertion_point = html_match.end()

        modified_html = (
            original_html[:insertion_point] +
            "\n" + overlay_html + "\n" +
            original_html[insertion_point:]
        )
        return modified_html

    # Попытка 3: Fallback - добавляем overlay в начало HTML
    # Ищем начало HTML контента (после DOCTYPE если есть)
    doctype_pattern = r'(<!DOCTYPE[^>]*>)'
    doctype_match = re.search(doctype_pattern, original_html, re.IGNORECASE)

    if doctype_match:
        # Есть DOCTYPE, вставляем overlay после него
        insertion_point = doctype_match.end()
        modified_html = (
            original_html[:insertion_point] +
            "\n" + overlay_html + "\n" +
            original_html[insertion_point:]
        )
        return modified_html

    # Последний fallback - просто добавляем overlay в начало
    return overlay_html + "\n" + original_html


# === ФУНКЦИИ ДЛЯ РАБОТЫ С API ===






async def improve_prompt_with_openrouter(original_prompt: str) -> Tuple[bool, str]:
    """
    Улучшает пользовательский промпт через OpenRouter API для более точной генерации HTML.
    Возвращает (success, improved_prompt_or_error).
    """
    timeout = aiohttp.ClientTimeout(total=60)  # Короткий таймаут для улучшения промпта
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
    }

    # Формируем запрос для улучшения промпта
    payload = {
        "model": OPENROUTER_MODEL,
        "messages": [
            {
                "role": "system",
                "content": PROMPT_IMPROVEMENT_SYSTEM
            },
            {
                "role": "user",
                "content": original_prompt
            }
        ],
        "max_tokens": 1000,
        "temperature": 0.3  # Низкая температура для более стабильного улучшения
    }

    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(OPENROUTER_API_URL, headers=headers, data=json.dumps(payload)) as resp:
                raw = await resp.text()
                if resp.status != 200:
                    return False, f"OpenRouter API error {resp.status}: {raw}"
                try:
                    data = json.loads(raw)
                except Exception:
                    return False, f"Invalid JSON from OpenRouter: {raw[:500]}"

                choices = data.get("choices") or []
                if not choices:
                    return False, f"No choices in OpenRouter response: {raw[:500]}"
                message = choices[0].get("message") or {}
                content = message.get("content")
                if isinstance(content, str):
                    return True, content.strip()
                return False, f"Unexpected content format from OpenRouter: {type(content)}"
    except asyncio.TimeoutError:
        return False, "OpenRouter API timeout during prompt improvement"
    except Exception as e:
        return False, f"OpenRouter API exception during prompt improvement: {e}"


async def call_openrouter_api(prompt: str, image_data: dict = None) -> Tuple[bool, str]:
    """
    Асинхронно вызывает OpenRouter Horizon Alpha для генерации HTML.
    Возвращает (success, response_or_error). Поддерживает передачу изображений.
    """
    timeout = aiohttp.ClientTimeout(total=1200)
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
    }

    # Формируем контент пользователя: системные правила + пользовательский запрос
    user_content = []
    system_plus_prompt = SYSTEM_PROMPT.strip()
    if prompt:
        system_plus_prompt += f"\n\nПользовательский запрос:\n{prompt}"
    user_content.append({"type": "text", "text": system_plus_prompt})

    # Добавляем изображение, если есть
    if image_data and isinstance(image_data, dict):
        mime = image_data.get("mime_type")
        data_b64 = image_data.get("data")
        if mime and data_b64:
            user_content.append({
                "type": "image_url",
                "image_url": {"url": f"data:{mime};base64,{data_b64}"}
            })

    # OpenRouter ожидает, что content для user может быть строкой ИЛИ массивом блоков.
    # Без system-ролей. Передаем всегда массив блоков для единообразия.
    payload = {
        "model": OPENROUTER_MODEL,
        "messages": [
            {
                "role": "user",
                "content": user_content
            }
        ]
    }

    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(OPENROUTER_API_URL, headers=headers, data=json.dumps(payload)) as resp:
                raw = await resp.text()
                if resp.status != 200:
                    return False, f"OpenRouter API error {resp.status}: {raw}"
                try:
                    data = json.loads(raw)
                except Exception:
                    return False, f"Invalid JSON from OpenRouter: {raw[:500]}"

                choices = data.get("choices") or []
                if not choices:
                    return False, f"No choices in OpenRouter response: {raw[:500]}"
                message = choices[0].get("message") or {}
                content = message.get("content")
                if isinstance(content, str):
                    return True, content
                if isinstance(content, list):
                    parts = []
                    for block in content:
                        if isinstance(block, dict) and block.get("type") == "text" and "text" in block:
                            parts.append(block["text"])
                    html_text = "\n".join(parts).strip()
                    if html_text:
                        return True, html_text
                    return False, f"Empty text content in OpenRouter response: {raw[:500]}"
                return False, f"Unexpected content format from OpenRouter: {type(content)}"
    except asyncio.TimeoutError:
        return False, "OpenRouter API timeout"
    except Exception as e:
        return False, f"OpenRouter API exception: {e}"


async def call_gemini_api(prompt: str, image_data: dict = None) -> Tuple[bool, str]:
    """
    Асинхронно вызывает Gemini 2.5 Pro API для генерации HTML через REST.
    Автоматически выбирает случайный API ключ и переключается при любых ошибках.
    Поддерживает передачу изображений.

    Args:
        prompt: Текстовый промпт для генерации
        image_data: Словарь с данными изображения в формате {"mime_type": str, "data": str}

    Returns:
        Tuple[bool, str]: (success, response_or_error)
    """
    if not OFFICIAL_GEMINI_API_KEYS:
        return False, "Gemini API ключи не настроены"

    # Множество для отслеживания уже использованных ключей
    used_keys = set()

    # Пробуем ключи, пока не будут испробованы все
    while len(used_keys) < len(OFFICIAL_GEMINI_API_KEYS):
        # Получаем случайный ключ, который еще не использовался
        available_keys = [key for key in OFFICIAL_GEMINI_API_KEYS if key not in used_keys]
        if not available_keys:
            break

        api_key = random.choice(available_keys)
        used_keys.add(api_key)

        # Показываем только первые 10 символов ключа для безопасности
        key_preview = api_key[:10] + "..." if len(api_key) > 10 else api_key
        log_admin(f"HTML Group: Calling Gemini API with random key: {key_preview}", level="debug")

        headers = {
            "x-goog-api-key": api_key,
            "Content-Type": "application/json"
        }

        # Формируем части контента
        content_parts = [{"text": prompt}]

        # Добавляем изображение, если оно есть
        if image_data and "mime_type" in image_data and "data" in image_data:
            content_parts.append({
                "inlineData": {
                    "mimeType": image_data["mime_type"],
                    "data": image_data["data"]
                }
            })

        data = {
            "contents": [{"parts": content_parts}],
            "systemInstruction": {
                "parts": [{"text": SYSTEM_PROMPT}]
            },
            "generationConfig": {
                "maxOutputTokens": 65536,
                "thinkingConfig": {"thinkingBudget": 32768}
            }
        }

        try:
            timeout = aiohttp.ClientTimeout(total=1200)  # 20 минут
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(GEMINI_API_URL, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if 'candidates' in result and len(result['candidates']) > 0:
                            candidate = result['candidates'][0]
                            if 'content' in candidate and 'parts' in candidate['content']:
                                content = candidate['content']['parts'][0]['text']
                                log_admin(f"HTML Group: Gemini API call successful with key: {key_preview}", level="debug")
                                return True, content
                            else:
                                log_admin(f"HTML Group: Invalid Gemini API response format with key: {key_preview}", level="error")
                                # Продолжаем с другим ключом
                                continue
                        else:
                            log_admin(f"HTML Group: No candidates in Gemini response with key: {key_preview}", level="error")
                            # Продолжаем с другим ключом
                            continue
                    else:
                        error_text = await response.text()
                        error_msg = f"Gemini API error {response.status}: {error_text}"
                        log_admin(f"HTML Group: {error_msg} with key: {key_preview}", level="error")
                        # Продолжаем с другим ключом
                        continue

        except asyncio.TimeoutError:
            log_admin(f"HTML Group: Gemini API timeout with key: {key_preview}", level="error")
            # Продолжаем с другим ключом
            continue

        except Exception as e:
            log_admin(f"HTML Group: Gemini API call exception with key: {key_preview}: {e}", level="error")
            # Продолжаем с другим ключом
            continue

    # Если все ключи не сработали
    return False, "Все Gemini API ключи недоступны или исчерпаны лимиты"


async def call_cerebras_api(prompt: str) -> Tuple[bool, str]:
    """
    Асинхронно вызывает Cerebras API для генерации HTML.
    Автоматически переключается между API ключами при ошибках лимитов.

    Returns:
        Tuple[bool, str]: (success, response_or_error)
    """
    global current_api_key_index

    data = {
        "model": CEREBRAS_MODEL,
        "messages": [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": prompt}
        ],
        **API_PARAMS
    }

    # Пробуем все API ключи
    for attempt in range(len(CEREBRAS_API_KEYS)):
        api_key = CEREBRAS_API_KEYS[current_api_key_index]
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        try:
            log_admin(f"HTML Group: Calling Cerebras API with key index {current_api_key_index}", level="debug")

            timeout = aiohttp.ClientTimeout(total=60)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(CEREBRAS_API_URL, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if 'choices' in result and len(result['choices']) > 0:
                            content = result['choices'][0]['message']['content']
                            log_admin(f"HTML Group: Cerebras API call successful with key index {current_api_key_index}", level="debug")
                            return True, content
                        else:
                            log_admin(f"HTML Group: Invalid Cerebras API response format", level="error")
                            return False, "Неверный формат ответа Cerebras API"

                    elif response.status == 429:  # Rate limit exceeded
                        log_admin(f"HTML Group: Cerebras rate limit exceeded for key index {current_api_key_index}, switching to next key", level="warning")
                        current_api_key_index = (current_api_key_index + 1) % len(CEREBRAS_API_KEYS)
                        continue

                    else:
                        error_text = await response.text()
                        error_msg = f"Cerebras API error {response.status}: {error_text}"
                        log_admin(f"HTML Group: {error_msg}", level="error")
                        return False, error_msg

        except asyncio.TimeoutError:
            log_admin(f"HTML Group: Cerebras API timeout with key index {current_api_key_index}", level="error")
            current_api_key_index = (current_api_key_index + 1) % len(CEREBRAS_API_KEYS)
            continue

        except Exception as e:
            log_admin(f"HTML Group: Cerebras API call exception with key index {current_api_key_index}: {e}", level="error")
            current_api_key_index = (current_api_key_index + 1) % len(CEREBRAS_API_KEYS)
            continue

    # Если все ключи не сработали
    return False, "Все Cerebras API ключи недоступны или исчерпаны лимиты"


# === УТИЛИТЫ ДЛЯ ОБРАБОТКИ ИЗОБРАЖЕНИЙ ===

def extract_image_from_message(message) -> dict:
    """
    Извлекает изображение из сообщения Telegram и возвращает его в формате base64.

    Args:
        message: Объект сообщения Telegram

    Returns:
        dict: Словарь с данными изображения {"mime_type": str, "data": str} или None
    """
    if message.content_type != "photo":
        return None

    try:
        # Берем фото наибольшего размера
        photo = message.photo[-1]
        file_info = bot.get_file(photo.file_id)
        base64_image = base64.b64encode(
            bot.download_file(file_info.file_path)
        ).decode("utf-8")

        # Определяем MIME тип по расширению файла
        mime_type = "image/jpeg"  # По умолчанию
        if file_info.file_path:
            ext = os.path.splitext(file_info.file_path)[1].lower()
            if ext == ".png":
                mime_type = "image/png"
            elif ext == ".webp":
                mime_type = "image/webp"
            elif ext == ".gif":
                mime_type = "image/gif"

        return {
            "mime_type": mime_type,
            "data": base64_image
        }

    except Exception as e:
        log_admin(f"HTML Group: Error extracting image from message: {e}", level="error")
        return None


# === ОБРАБОТЧИК КОМАНДЫ ===

@bot.message_handler(commands=["html"])
def handle_html_command(message):
    """
    Асинхронная обертка для обработчика команды /html.
    Запускает каждый запрос в отдельном потоке для истинного параллелизма.
    """
    # Импортируем thread pool manager для параллельного выполнения
    from thread_pool_manager import submit_task

    def run_async_handler():
        """Функция для выполнения в отдельном потоке."""
        try:
            # Создаем новый event loop для этого потока
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(handle_html_command_async(message))
        except Exception as e:
            log_admin(f"HTML Group: Error in async handler: {e}", level="error")
            try:
                bot.reply_to(message, f"❌ Произошла ошибка при обработке запроса: {str(e)}")
            except:
                pass
        finally:
            try:
                loop.close()
            except:
                pass

    # Запускаем обработчик в отдельном потоке для параллельного выполнения
    submit_task(run_async_handler)


async def handle_html_command_async(message):
    """
    Обработчик команды /html для генерации HTML сайтов в группах.
    Работает только в группах, в личных сообщениях выдает ошибку.
    """
    # Импорт утилит
    from handlers import is_command_for_me
    from access_control import check_message_access
    
    # Проверяем, предназначена ли команда для этого бота
    if not is_command_for_me(message):
        return
    
    # Проверяем доступ пользователя
    if not check_message_access(message):
        return
    
    # Команда работает только в группах
    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "❌ Команда /html работает только в группах.")
        return

    # Извлекаем изображение из сообщения (если есть) - делаем это в самом начале
    image_data = extract_image_from_message(message)

    # Проверяем ограничения по времени и выбираем API
    user_id = message.from_user.id

    # Сначала проверяем, может ли пользователь использовать Gemini
    can_use_gemini, gemini_seconds_remaining = check_gemini_rate_limit(user_id)

    # Затем проверяем лимит Cerebras
    can_use_cerebras, cerebras_seconds_remaining = check_rate_limit(user_id)

    # Если есть изображение, проверяем только Gemini
    if image_data and not can_use_gemini:
        minutes_remaining = int(gemini_seconds_remaining // 60)
        seconds_remaining = int(gemini_seconds_remaining % 60)

        if minutes_remaining > 0:
            time_text = f"{minutes_remaining} мин {seconds_remaining} сек"
        else:
            time_text = f"{seconds_remaining} сек"

        warning_message = bot.reply_to(
            message,
            f"⏰ Для обработки изображений доступен только Gemini.\n"
            f"Следующий запрос через: {time_text}"
        )

        # Автоматически удаляем предупреждение через 10 секунд
        delete_message_after_delay(
            warning_message.chat.id,
            warning_message.message_id,
            WARNING_MESSAGE_DELETE_DELAY
        )

        log_admin(
            f"HTML Group: Gemini rate limited for user {user_id} with image",
            level="info"
        )
        return

    # Если нет изображения и ни один API недоступен
    if not image_data and not can_use_gemini and not can_use_cerebras:
        # Показываем время до ближайшего доступного API
        if gemini_seconds_remaining <= cerebras_seconds_remaining:
            # Gemini доступен раньше
            minutes_remaining = int(gemini_seconds_remaining // 60)
            seconds_remaining = int(gemini_seconds_remaining % 60)
            api_name = "Gemini"
        else:
            # Cerebras доступен раньше
            minutes_remaining = int(cerebras_seconds_remaining // 60)
            seconds_remaining = int(cerebras_seconds_remaining % 60)
            api_name = "Cerebras"

        if minutes_remaining > 0:
            time_text = f"{minutes_remaining} мин {seconds_remaining} сек"
        else:
            time_text = f"{seconds_remaining} сек"

        warning_message = bot.reply_to(
            message,
            f"⏰ Все API временно недоступны.\n"
            f"Следующий {api_name} через: {time_text}"
        )

        # Автоматически удаляем предупреждение через 10 секунд
        delete_message_after_delay(
            warning_message.chat.id,
            warning_message.message_id,
            WARNING_MESSAGE_DELETE_DELAY
        )

        log_admin(
            f"HTML Group: All APIs rate limited for user {user_id}",
            level="info"
        )
        return

    # Извлекаем текст после команды
    command_text = message.text.strip() if message.text else "/html"
    prompt = command_text[5:].strip() if len(command_text) > 5 else ""  # убираем "/html "

    # Проверяем, что есть либо текст, либо изображение
    if not prompt and not image_data:
        bot.reply_to(message, "❌ Укажите описание сайта после команды или прикрепите изображение.\nПример: /html создай сайт портфолио веб-разработчика")
        return

    # Если есть только изображение без текста, создаем базовый промпт
    if not prompt and image_data:
        prompt = "Создай сайт на основе прикрепленного изображения"
    
    # Логируем запрос
    user_info = f"@{message.from_user.username}" if message.from_user.username else f"ID:{message.from_user.id}"
    group_info = f"'{message.chat.title}' (ID:{message.chat.id})"

    # Определяем, какой API использовать (приоритет у OpenRouter)
    # Если есть изображение, OpenRouter поддерживает image_url — оставляем приоритет у OpenRouter
    use_openrouter = True
    api_name = "GPT-5" if use_openrouter else "Cerebras"

    # Логируем запрос с информацией об изображении
    image_info = " (с изображением)" if image_data else ""
    log_admin(f"HTML Group: Request from {user_info} in group {group_info} using {api_name}{image_info}: {prompt[:100]}...", level="info")

    # Обновляем время последнего запроса пользователя
    if use_openrouter:
        update_gemini_request_time(user_id)
    else:
        update_user_request_time(user_id)

    try:
        # Инициализируем переменную для улучшенного промпта
        improved_prompt = prompt

        if use_openrouter:
            # Используем OpenRouter - сначала улучшаем промпт, затем генерируем
            status_text = "🧠 Анализирую запрос и генерирую сайт с GPT-5..."
            if image_data:
                status_text += " (с учетом изображения)"
            status_message = bot.reply_to(message, status_text)

            # Сначала улучшаем промпт (только если нет изображения, чтобы не усложнять)
            if not image_data:
                log_admin(f"HTML Group: Improving prompt for {user_info}: {prompt}", level="debug")
                improvement_success, improvement_result = await improve_prompt_with_openrouter(prompt)

                if improvement_success:
                    improved_prompt = improvement_result
                    log_admin(f"HTML Group: Improved prompt for {user_info}: {improved_prompt}", level="info")
                else:
                    log_admin(f"HTML Group: Failed to improve prompt for {user_info}: {improvement_result}", level="warning")
                    # Продолжаем с оригинальным промптом
            else:
                log_admin(f"HTML Group: Skipping prompt improvement due to image attachment", level="debug")

            # Вызываем OpenRouter API асинхронно с улучшенным промптом
            api_success, api_response = await call_openrouter_api(improved_prompt, image_data)

        else:
            # Используем Cerebras - с прогресс-баром и показом времени до Gemini
            initial_bar = "░" * 20  # Пустой прогресс-бар
            initial_message = f"🥵 Генерирую HTML сайт...\n\n{initial_bar} 0.0%"
            status_message = bot.reply_to(message, initial_message)

            # Запускаем анимацию прогресс-бара и API вызов параллельно
            animation_task = asyncio.create_task(
                animate_progress_bar(
                    status_message.chat.id,
                    status_message.message_id,
                    duration=4.0,
                    user_id=user_id
                )
            )

            api_task = asyncio.create_task(call_cerebras_api(prompt))

            # Ждем завершения анимации (4 секунды)
            await animation_task

            # Проверяем, завершился ли API вызов
            if not api_task.done():
                # API еще работает, показываем 100% и ждем
                final_bar = "█" * 20
                bot.edit_message_text(
                    f"🥵 Генерирую HTML сайт...\n\n{final_bar} 100.0%",
                    chat_id=status_message.chat.id,
                    message_id=status_message.message_id
                )

            # Ждем завершения API
            api_success, api_response = await api_task

        # API завершился, обрабатываем результат
        if not api_success:
            error_text = f"❌ Ошибка генерации HTML: {api_response}"

            bot.edit_message_text(
                error_text,
                chat_id=status_message.chat.id,
                message_id=status_message.message_id
            )
            # Удаляем сообщение об ошибке через 10 секунд
            delete_message_after_delay(
                status_message.chat.id,
                status_message.message_id,
                10
            )
            return

        # Очищаем ответ от мусорных тегов
        clean_html = clean_html_response(api_response)

        # Добавляем пометку для файлов, созданных с OpenRouter
        if use_openrouter:
            # Показываем оба промпта в комментарии, если они отличаются
            if improved_prompt != prompt:
                comment = f"<!-- openrouter/horizon-alpha\n________________\nоригинальный промпт: {prompt}\nулучшенный промпт: {improved_prompt} -->\n"
            else:
                comment = f"<!-- openrouter/horizon-alpha\n________________\nпромпт: {prompt} -->\n"
            clean_html = comment + clean_html

        # === ИНТЕГРАЦИЯ OVERLAY ПЛАШКИ MEAN ===
        try:
            # Извлекаем данные пользователя и промпт
            user_name = get_user_display_name(message)
            message_link = create_message_link(message.chat.id, message.message_id)
            prompt_display = truncate_prompt(prompt)

            # Генерируем overlay HTML
            overlay_html = generate_overlay_html(user_name, prompt_display, prompt, message_link)

            # Встраиваем overlay в HTML
            clean_html = inject_overlay_into_html(clean_html, overlay_html)

            log_admin(f"HTML Group: Successfully integrated MEAN overlay for user {user_name}", level="debug")

        except Exception as overlay_error:
            # Если интеграция overlay не удалась, продолжаем с оригинальным HTML
            log_admin(f"HTML Group: Failed to integrate MEAN overlay: {overlay_error}", level="warning")
            # clean_html остается без изменений - обратная совместимость

        if not clean_html:
            bot.edit_message_text(
                "❌ Получен пустой HTML код",
                chat_id=status_message.chat.id,
                message_id=status_message.message_id
            )
            # Удаляем сообщение об ошибке через 10 секунд
            delete_message_after_delay(
                status_message.chat.id,
                status_message.message_id,
                10
            )
            return

        # Генерируем имя файла
        filename = generate_filename_from_prompt(prompt)

        # Обновляем статус - готово
        if use_openrouter:
            # Для OpenRouter - простое сообщение
            bot.edit_message_text(
                f"✅ HTML сайт готов с GPT-5!\n📄 Файл: {filename}",
                chat_id=status_message.chat.id,
                message_id=status_message.message_id
            )
        else:
            # Для Cerebras - с финальным прогресс-баром
            final_bar = "█" * 20  # Полностью заполненный прогресс-бар
            bot.edit_message_text(
                f"✅ HTML сайт готов!\n\n{final_bar} 100.0%\n📄 Файл: {filename}",
                chat_id=status_message.chat.id,
                message_id=status_message.message_id
            )

        # Отправляем HTML файл (без подписи)
        from io import BytesIO
        html_file = BytesIO(clean_html.encode('utf-8'))
        html_file.name = filename

        bot.send_document(
            chat_id=message.chat.id,
            document=html_file,
            reply_to_message_id=message.message_id
        )

        log_admin(f"HTML Group: Successfully generated and sent HTML file '{filename}' for {user_info} using {api_name}", level="info")
        
    except Exception as e:
        log_admin(f"HTML Group: Error processing request: {e}", level="error")
        try:
            bot.edit_message_text(
                f"❌ Произошла ошибка при обработке запроса: {str(e)}",
                chat_id=status_message.chat.id,
                message_id=status_message.message_id
            )
        except:
            bot.reply_to(message, f"❌ Произошла ошибка при обработке запроса: {str(e)}")


# === ИНИЦИАЛИЗАЦИЯ МОДУЛЯ ===

def initialize_html_group_module():
    """
    Инициализация модуля html_group.
    Вызывается при импорте модуля.
    """
    log_admin("HTML Group: Module initialized", level="info")
    log_admin(f"HTML Group: Loaded {len(CEREBRAS_API_KEYS)} Cerebras API keys", level="debug")
    log_admin(f"HTML Group: Loaded {len(OFFICIAL_GEMINI_API_KEYS)} Gemini API keys", level="debug")
    log_admin("HTML Group: Module ready with GPT-5 as primary for /html", level="info")


# Инициализируем модуль при импорте
initialize_html_group_module()
