# Оптимизированная версия подкастового бота
# Исправлены проблемы с производительностью:
# - Увеличены интервалы фоновых задач
# - Оптимизирован polling
# - Улучшено управление ресурсами

import subprocess
import sys
import time
import traceback
import requests
import telebot
import threading
import os # Добавлено для проверки существования файла лога
from console_cleaner import clear_console, minimize_console_buffer

# Import functions and variables from other modules
# Важно, чтобы bot_globals импортировался одним из первых, чтобы логгер настроился
from bot_globals import (
    bot, log_admin, audio_video_groups, media_groups,
    user_forward_batch, user_request_buffer, log_file_path,
    load_user_settings, save_user_settings,
    audio_video_group_lock, media_group_lock,
    user_forward_batch_lock, user_request_buffer_lock,
    get_bot_username  # ЭТАП 2: Добавлен импорт кэшированной функции
)
from telegraph_helpers import setup_telegraph
from config import TELEGRAPH_SHORT_NAME, AUTO_CLEANUP_ENABLED

# IMPORTANT: Import admin_system to load bot data at startup
import admin_system

# IMPORTANT: Import handlers to register them
import handlers

def start_bot():
    """Функция для запуска бота из других модулей"""
    # Минимизируем буфер консоли для экономии памяти
    minimize_console_buffer()
    log_admin("🚀 Запуск бота через start_bot()...", level="warning")
    
    # Весь код запуска перенесен в эту функцию
    _run_bot_initialization()

def _run_bot_initialization():
    """Внутренняя функция с основной логикой запуска бота"""

    # Проверка существования файла лога после инициализации логгера в bot_globals
    # Эта проверка больше для информации, сам файл создается FileHandler'ом
    if os.path.exists(log_file_path):
        log_admin(f"Log file is expected at: {log_file_path}", level="info")
    else:
        # Попытка создать пустой файл, чтобы проверить права на запись в директорию
        try:
            with open(log_file_path, 'a') as f:
                pass # Просто создать/открыть и закрыть
            log_admin(f"Log file was not found, but successfully created a placeholder at: {log_file_path}. Check permissions if still no logs.", level="info")
            # Важно: FileHandler в logging создаст файл сам, если его нет, при первой записи.
            # Эта проверка здесь - дополнительная, на случай если FileHandler не может его создать.
        except Exception as e:
            log_admin(f"Could not create a placeholder log file at {log_file_path}. Error: {e}. Check directory permissions.", level="error")


    # Check for ffmpeg
    log_admin("Checking for ffmpeg...", level="info")
    try:
        # Сначала проверяем глобальную папку bin (относительно корня проекта)
        current_dir = os.getcwd()
        parent_dir = os.path.dirname(current_dir)  # Поднимаемся на уровень выше (корень проекта)
        global_bin_path = os.path.join(parent_dir, "bin")

        env = os.environ.copy()

        # Добавляем глобальную папку bin в PATH если она существует
        if os.path.exists(global_bin_path):
            env['PATH'] = global_bin_path + os.pathsep + env.get('PATH', '')
            log_admin(f"Added global bin directory to PATH: {global_bin_path}", level="info")

        # Также добавляем системные пути
        env['PATH'] = '/usr/bin:' + env.get('PATH', '')

        process = subprocess.run(
            ['ffmpeg', '-version'],
            check=True,
            capture_output=True,
            timeout=15, # Увеличен тайм-аут
            env=env
        )
        log_admin("ffmpeg found.", level="info")
    except (FileNotFoundError, subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
        log_admin("ffmpeg не найден - обработка аудио/видео будет недоступна", level="info")
        # Не выходим, а просто выводим предупреждение
    except Exception as e:
        log_admin(f"Unexpected error during ffmpeg check: {e}", level="error")
        log_admin("Warning: Could not verify ffmpeg, audio/video processing might not work.", level="warning")



    # Initialize Telegraph client
    log_admin("Initializing telegra.ph client...", level="info")
    try:
        # ЭТАП 3: Улучшенная обработка исключений для получения bot username
        log_admin("Attempting to get bot username for Telegraph initialization...", level="debug")
        bot_username = get_bot_username()

        if bot_username == 'unknown_bot':
            log_admin("Bot username is 'unknown_bot', will use fallback for Telegraph", level="warning")
            bot_username = None
        elif bot_username:
            log_admin(f"Successfully obtained bot username for Telegraph: @{bot_username}", level="debug")
        else:
            log_admin("Bot username is empty, will use fallback for Telegraph", level="warning")
            bot_username = None

    except Exception as e:
        # Детальное логирование различных типов ошибок
        error_type = type(e).__name__
        error_msg = str(e)

        if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
            log_admin(f"Timeout error while getting bot username for Telegraph: {error_type}: {error_msg}", level="error")
            log_admin("This may indicate network connectivity issues with Telegram API", level="warning")
        elif "connection" in error_msg.lower() or "network" in error_msg.lower():
            log_admin(f"Network error while getting bot username for Telegraph: {error_type}: {error_msg}", level="error")
            log_admin("Telegraph will be initialized with fallback name", level="info")
        else:
            log_admin(f"Unexpected error while getting bot username for Telegraph: {error_type}: {error_msg}", level="error")

        # Graceful degradation: используем fallback значение
        bot_username = None
        log_admin("Using fallback configuration for Telegraph initialization", level="info")
    if not setup_telegraph(short_name=bot_username or TELEGRAPH_SHORT_NAME):
        log_admin("telegra.ph не подключен - публикация отключена", level="info")
    else:
        log_admin("telegra.ph client ready.", level="info")

    # Загрузка настроек пользователей
    log_admin("Загрузка настроек пользователей...", level="info")
    if load_user_settings():
        log_admin("Настройки пользователей успешно загружены.", level="info")
    else:
        log_admin("Используются настройки пользователей по умолчанию.", level="info")

    # Инициализация базы данных
    log_admin("Инициализация базы данных...", level="info")
    try:
        from database import init_database
        if init_database():
            log_admin("База данных успешно инициализирована.", level="info")
        else:
            log_admin("Ошибка инициализации базы данных.", level="error")
    except Exception as e:
        log_admin("Не удалось инициализировать базу данных", level="critical")

    # Объединенный фоновый планировщик для всех периодических задач
    def unified_background_scheduler():
        """
        Объединенный планировщик для всех фоновых задач.
        Уменьшает количество потоков и оптимизирует использование ресурсов.
        ЭТАП 2: Увеличена частота GC до каждых 15 минут, добавлена очистка буферов каждые 20 минут.
        ЭТАП 3: Добавлен механизм graceful shutdown для предотвращения зависания.
        """
        console_clear_counter = 0

        # Импорты внутри функции для ленивой загрузки

        log_admin("Unified background scheduler started", level="info")

        while True:
            try:
                # Основной цикл каждые 5 минут для более частой очистки
                time.sleep(300)  # 5 минут



                # Периодический разогрев соединений каждые 12 циклов (3 часа)
                if console_clear_counter % 12 == 0 and console_clear_counter > 0:
                    try:
                        from genai_client import client_manager
                        # Быстрый разогрев 1 ключа для поддержания соединений
                        warmup_results = client_manager.warmup_connections(max_keys=1)
                        if any(warmup_results.values()):
                            log_admin("Periodic connection warmup successful", level="debug")
                    except Exception as e:
                        log_admin(f"Error in periodic warmup: {e}", level="debug")

                # Сброс состояний ключей каждые 24 цикла (6 часов) для восстановления после rate limit
                if console_clear_counter % 24 == 0 and console_clear_counter > 0:
                    try:
                        from genai_client import client_manager
                        client_manager.reset_key_states()
                        log_admin("API key states reset for recovery", level="debug")
                    except Exception as e:
                        log_admin(f"Error resetting key states: {e}", level="debug")

                # Очистка консоли каждые 48 циклов (12 часов)
                if console_clear_counter % 48 == 0 and console_clear_counter > 0:
                    try:
                        clear_console()
                        log_admin("🧹 Консоль очищена для экономии памяти", level="info")
                    except Exception as e:
                        log_admin(f"Error clearing console: {e}", level="warning")



                console_clear_counter += 1

                # Сброс счетчиков для предотвращения переполнения
                if console_clear_counter > 1000:
                    console_clear_counter = 1

            except Exception as e:
                log_admin(f"Error in unified background scheduler: {e}", level="error")
                time.sleep(300)  # При ошибке ждем 5 минут

    # Запуск объединенного планировщика
    unified_thread = threading.Thread(target=unified_background_scheduler, daemon=True)
    unified_thread.start()

    # Планировщик очистки памяти уже интегрирован в unified_background_scheduler
    # и cleanup_scheduler, поэтому отдельный планировщик не нужен

    log_admin("Unified background scheduler thread started (оптимизированный).", level="info")

    # Запуск разогрева соединений в фоновом режиме для быстрого отклика
    def warmup_connections_background():
        """Фоновый разогрев соединений для быстрого отклика после простоя."""
        try:
            # Ждем 5 секунд после запуска, чтобы основные системы инициализировались
            time.sleep(5)
            
            from genai_client import client_manager
            log_admin("🔥 Запуск разогрева соединений для быстрого отклика...", level="info")
            
            # Разогреваем первые 2 ключа для быстрого старта
            warmup_results = client_manager.warmup_connections(max_keys=2)
            successful_count = sum(1 for success in warmup_results.values() if success)
            
            if successful_count > 0:
                log_admin(f"✅ Разогрев завершен: {successful_count} соединений готовы", level="info")
            else:
                log_admin("⚠️ Разогрев не удался, но бот продолжит работу", level="warning")
                
        except Exception as e:
            log_admin(f"Ошибка при разогреве соединений: {e}", level="warning")

    # Запускаем разогрев в отдельном потоке
    warmup_thread = threading.Thread(target=warmup_connections_background, daemon=True)
    warmup_thread.start()

    # Podcast queue update scheduler removed - podcasts now run directly without queue





    log_admin(f"Bot sh is starting...", level="info")
    # === ИНИЦИАЛИЗАЦИЯ ЗАВЕРШЕНА ===
    # log_admin("Bot started polling.", level="info") # This line is removed

    # Основной цикл с улучшенной обработкой ошибок
    restart_attempts = 0
    max_restart_attempts = 10
    
    while restart_attempts < max_restart_attempts:
        try:
            log_admin("MAIN: Bot polling starting...", level="info")

            # Счетчики для адаптивного переподключения
            consecutive_timeout_errors = 0
            consecutive_connection_errors = 0
            consecutive_api_errors = 0

            # Используем infinity_polling для автоматического переподключения
            try:
                # Увеличенный timeout для предотвращения ложных срабатываний при сжатии логов
                # Добавлены более надежные параметры для стабильности соединения
                bot.infinity_polling(
                    interval=2,  # Немного больше интервал для снижения нагрузки
                    timeout=60,  # Уменьшен timeout для более быстрого обнаружения проблем
                    long_polling_timeout=20,  # Добавлен long polling timeout
                    skip_pending=True,
                    allowed_updates=['message', 'callback_query', 'inline_query', 'chosen_inline_result',
                                   'message_reaction', 'message_reaction_count'],
                    restart_on_change=False  # Отключаем автоперезапуск для лучшего контроля
                )
                # Если дошли сюда - polling завершился нормально
                break
                
            except KeyboardInterrupt:
                log_admin("🛑 Получен сигнал остановки бота", level="info")
                break
                
            except requests.exceptions.ConnectionError as conn_err:
                consecutive_connection_errors += 1
                log_admin(f"🔗 Ошибка соединения в infinity_polling (попытка #{consecutive_connection_errors}): {conn_err}", level="warning")
                delay = min(consecutive_connection_errors * 2, 30)
                log_admin(f"🔄 Переподключение через {delay}с...", level="info")
                time.sleep(delay)
                restart_attempts += 1
                continue
                
            except requests.exceptions.ReadTimeout as timeout_err:
                consecutive_timeout_errors += 1
                log_admin(f"⏱️ Таймаут в infinity_polling (попытка #{consecutive_timeout_errors}): {timeout_err}", level="warning")
                delay = min(consecutive_timeout_errors * 2, 30)
                log_admin(f"🔄 Переподключение через {delay}с...", level="info")
                time.sleep(delay)
                restart_attempts += 1
                continue
                
            except Exception as e:
                import traceback
                error_str = str(e).lower()
                # Специальная обработка для ServerDisconnectedError
                if "server disconnected" in error_str or "serverdisconnectederror" in error_str:
                    consecutive_connection_errors += 1
                    log_admin(f"🔌 Сервер отключился (попытка #{consecutive_connection_errors}): {e}", level="warning")
                    delay = min(consecutive_connection_errors * 3, 45)  # Больше задержка для дисконнектов
                    log_admin(f"🔄 Переподключение через {delay}с...", level="info")
                    time.sleep(delay)
                    restart_attempts += 1
                    continue
                else:
                    log_admin(f"❌ Критическая ошибка в infinity_polling: {e}\n{traceback.format_exc()}", level="error")
                    time.sleep(5)
                    restart_attempts += 1
                    continue
                
        except requests.exceptions.ReadTimeout:
            consecutive_timeout_errors += 1
            log_admin(f"❌ Таймаут подключения к Telegram (попытка #{consecutive_timeout_errors})", level="warning")

            # Адаптивная задержка для таймаутов
            delay = min(consecutive_timeout_errors * 2, 30) # Экспоненциальная задержка до 30с

            log_admin(f"🔄 Переподключение через {delay}с...", level="info")
            time.sleep(delay)
            restart_attempts += 1
            
        except requests.exceptions.ConnectionError:
            consecutive_connection_errors += 1
            log_admin(f"❌ Ошибка сети при подключении к Telegram (попытка #{consecutive_connection_errors})", level="warning")

            # Адаптивная задержка для ошибок сети
            delay = min(consecutive_connection_errors * 2, 60) # Экспоненциальная задержка до 60с

            log_admin(f"🔄 Переподключение через {delay}с...", level="info")
            time.sleep(delay)
            restart_attempts += 1
            
        except telebot.apihelper.ApiTelegramException as e_api:
            consecutive_api_errors += 1
            log_admin(f"❌ Ошибка Telegram API (попытка #{consecutive_api_errors}): {e_api}", level="error")

            # Для API ошибок немного больше задержка
            if consecutive_api_errors == 1:
                delay = 1.0
            elif consecutive_api_errors <= 3:
                delay = 2.0
            elif consecutive_api_errors <= 5:
                delay = 5.0
            else:
                delay = 10.0  # Максимальная задержка 10 секунд

            log_admin(f"🔄 Переподключение через {delay}с...", level="info")
            time.sleep(delay)
            restart_attempts += 1

        except Exception as e_inner:
            import traceback
            log_admin(f"💥 Критическая ошибка: {e_inner}\n{traceback.format_exc()}", level="critical")

            # Cleanup hanging processes before restart
            try:
                from utils import cleanup_hanging_ffmpeg_processes
                cleanup_hanging_ffmpeg_processes()
                log_admin("Emergency FFmpeg cleanup completed", level="info")
            except Exception as cleanup_error:
                log_admin(f"Error in emergency cleanup: {cleanup_error}", level="warning")

            # Для критических ошибок даем больше времени на восстановление
            log_admin("🔄 Перезапуск polling через 5с (критическая ошибка)...", level="info")
            time.sleep(5)
            restart_attempts += 1

    if restart_attempts >= max_restart_attempts:
        log_admin(f"💀 Превышено максимальное количество попыток перезапуска ({max_restart_attempts}). Завершение работы.", level="critical")
    else:
        log_admin("⚠️ Polling завершен", level="warning")

    log_admin("Цикл polling завершен или прерван", level="critical")

    # --- Cleanup on exit ---
    log_admin("Stopping bot...", level="info")

    # Останавливаем подкастные воркеры
    try:
        from processing_core import shutdown_podcast_workers
        shutdown_podcast_workers()
    except Exception as e:
        log_admin(f"Error shutting down podcast workers: {e}", level="warning")

    # Останавливаем thread pool manager
    try:
        from thread_pool_manager import thread_pool_manager
        thread_pool_manager.shutdown(wait=False)
    except Exception as e:
        log_admin(f"Error shutting down thread pool: {e}", level="warning")

    with audio_video_group_lock:
        for user_id_key in list(audio_video_groups.keys()): # Изменено имя переменной во избежание конфликта
            if audio_video_groups[user_id_key]['timer']:
                audio_video_groups[user_id_key]['timer'].cancel()
                log_admin(f"Cancelled pending audio timer for user {user_id_key} on stop.", level="debug")

    with media_group_lock:
        for group_id in list(media_groups.keys()):
            if media_groups[group_id]['timer']:
                media_groups[group_id]['timer'].cancel()
                log_admin(f"Cancelled pending media group timer {group_id} on stop.", level="debug")

    with user_forward_batch_lock:
        for user_id_key_fw in list(user_forward_batch.keys()): # Изменено имя переменной
            if user_forward_batch[user_id_key_fw]['timer']:
                user_forward_batch[user_id_key_fw]['timer'].cancel()
                log_admin(f"Cancelled pending forward batch timer for user {user_id_key_fw} on stop.", level="debug")

    with user_request_buffer_lock:
        for user_id_key_req in list(user_request_buffer.keys()): # Изменено имя переменной
            if user_request_buffer[user_id_key_req]['timer']:
                user_request_buffer[user_id_key_req]['timer'].cancel()
                log_admin(f"Cancelled pending request buffer timer for user {user_id_key_req} on stop.", level="debug")

    # Сохранение настроек пользователей при завершении работы
    log_admin("Сохранение настроек пользователей перед завершением...", level="info")
    if save_user_settings():
        log_admin("Настройки пользователей успешно сохранены.", level="info")
    else:
        log_admin("Ошибка при сохранении настроек пользователей!", level="error")

    log_admin("✅ Бот остановлен", level="warning")

# --- Main Execution Block ---
if __name__ == '__main__':
    # При запуске напрямую используем функцию start_bot
    start_bot()
