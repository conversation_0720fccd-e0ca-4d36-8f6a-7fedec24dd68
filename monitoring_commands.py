#!/usr/bin/env python3
"""
Скрипт для мониторинга состояния бота и выявления проблем
"""

import psutil
import threading
import time
import os
import subprocess
from datetime import datetime

def check_bot_processes():
    """Проверяет процессы бота"""
    print("=== Процессы бота ===")
    
    bot_processes = []
    ffmpeg_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent']):
        try:
            cmdline = ' '.join(proc.info['cmdline'] or [])
            
            # Ищем процессы бота
            if 'main.py' in cmdline or 'python' in proc.info['name'].lower():
                if 'bot' in cmdline.lower() or 'main.py' in cmdline:
                    bot_processes.append(proc.info)
            
            # Ищем FFmpeg процессы
            if proc.info['name'] == 'ffmpeg':
                ffmpeg_processes.append(proc.info)
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"Найдено процессов бота: {len(bot_processes)}")
    for proc in bot_processes:
        memory_mb = proc['memory_info'].rss / 1024 / 1024
        print(f"  PID {proc['pid']}: {memory_mb:.1f} MB RAM, CPU: {proc['cpu_percent']:.1f}%")
    
    print(f"\nНайдено FFmpeg процессов: {len(ffmpeg_processes)}")
    for proc in ffmpeg_processes:
        print(f"  PID {proc['pid']}: {' '.join(proc['cmdline'][:3])}...")
    
    return len(bot_processes), len(ffmpeg_processes)

def check_threads():
    """Проверяет количество потоков"""
    print("\n=== Потоки ===")
    
    current_process = psutil.Process()
    thread_count = current_process.num_threads()
    print(f"Текущих потоков: {thread_count}")
    
    # Проверяем активные потоки Python
    active_threads = threading.active_count()
    print(f"Активных Python потоков: {active_threads}")
    
    # Выводим имена потоков
    for thread in threading.enumerate():
        print(f"  - {thread.name}: {'daemon' if thread.daemon else 'main'}")
    
    return thread_count, active_threads

def check_memory():
    """Проверяет использование памяти"""
    print("\n=== Память ===")
    
    # Системная память
    memory = psutil.virtual_memory()
    print(f"Системная память: {memory.percent}% ({memory.used / 1024**3:.1f} GB / {memory.total / 1024**3:.1f} GB)")
    
    # Память текущего процесса
    current_process = psutil.Process()
    process_memory = current_process.memory_info()
    print(f"Память процесса: {process_memory.rss / 1024**2:.1f} MB")
    
    return memory.percent, process_memory.rss / 1024**2

def check_log_file():
    """Проверяет файл логов"""
    print("\n=== Лог файл ===")
    
    log_file = "bot_activity.txt"
    if os.path.exists(log_file):
        stat = os.stat(log_file)
        size_mb = stat.st_size / 1024 / 1024
        modified_time = datetime.fromtimestamp(stat.st_mtime)
        print(f"Файл логов: {size_mb:.1f} MB, изменен: {modified_time}")
        
        # Читаем последние 10 строк
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print("\nПоследние записи:")
                for line in lines[-5:]:
                    print(f"  {line.strip()}")
        except Exception as e:
            print(f"Ошибка чтения лога: {e}")
    else:
        print("Файл логов не найден!")

def monitor_continuously(interval=60):
    """Непрерывный мониторинг"""
    print(f"Запуск непрерывного мониторинга (интервал: {interval} сек)")
    print("Нажмите Ctrl+C для остановки\n")
    
    try:
        while True:
            print(f"\n{'='*50}")
            print(f"Мониторинг в {datetime.now().strftime('%H:%M:%S')}")
            print(f"{'='*50}")
            
            bot_count, ffmpeg_count = check_bot_processes()
            thread_count, active_threads = check_threads()
            memory_percent, process_memory = check_memory()
            
            # Предупреждения
            warnings = []
            if ffmpeg_count > 5:
                warnings.append(f"Много FFmpeg процессов: {ffmpeg_count}")
            if thread_count > 100:
                warnings.append(f"Много потоков: {thread_count}")
            if memory_percent > 90:
                warnings.append(f"Мало системной памяти: {memory_percent}%")
            if process_memory > 2000:
                warnings.append(f"Много памяти процесса: {process_memory:.0f} MB")
            
            if warnings:
                print(f"\n⚠️  ПРЕДУПРЕЖДЕНИЯ:")
                for warning in warnings:
                    print(f"  - {warning}")
            else:
                print(f"\n✅ Все в норме")
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\nМониторинг остановлен")

def main():
    """Основная функция"""
    print("Мониторинг состояния бота")
    print("=" * 30)
    
    check_bot_processes()
    check_threads() 
    check_memory()
    check_log_file()
    
    print(f"\n{'='*50}")
    choice = input("Запустить непрерывный мониторинг? (y/N): ")
    if choice.lower() in ['y', 'yes', 'да']:
        interval = input("Интервал в секундах (по умолчанию 60): ")
        try:
            interval = int(interval) if interval else 60
        except ValueError:
            interval = 60
        monitor_continuously(interval)

if __name__ == "__main__":
    main() 