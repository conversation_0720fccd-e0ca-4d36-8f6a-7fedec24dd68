"""
Модуль мониторинга производительности для Gemini API.
Собирает метрики производительности, отслеживает время ответа и успешность запросов.
"""

import time
import threading
from collections import deque, defaultdict
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import statistics

try:
    from bot_globals import log_admin
except ImportError:
    def log_admin(message, level="info"):
        print(f"[{level.upper()}] {message}")

import config


@dataclass
class RequestMetrics:
    """Метрики одного запроса."""
    timestamp: float
    duration: float
    success: bool
    api_key_suffix: str
    model: str
    error_type: Optional[str] = None
    proxy_used: bool = False
    retry_count: int = 0


@dataclass
class AggregatedMetrics:
    """Агрегированные метрики."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    success_rate: float = 0.0
    slow_requests: int = 0
    proxy_requests: int = 0
    rate_limit_errors: int = 0
    retry_attempts: int = 0


class PerformanceMonitor:
    """
    Монитор производительности для отслеживания метрик Gemini API.
    Thread-safe реализация с поддержкой скользящего окна метрик.
    """
    
    def __init__(self):
        """Инициализация монитора производительности."""
        self._enabled = getattr(config, 'GENAI_ENABLE_METRICS', True)
        self._window_size = getattr(config, 'GENAI_METRICS_WINDOW_SIZE', 25)  # Уменьшено с 100
        self._slow_threshold = getattr(config, 'GENAI_SLOW_REQUEST_THRESHOLD', 10.0)
        
        # Thread-safe хранилища метрик
        self._lock = threading.RLock()
        self._request_history: deque = deque(maxlen=self._window_size)
        self._api_key_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=15))  # Уменьшено с 50
        self._model_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=15))  # Уменьшено с 50
        
        # Счетчики для быстрого доступа
        self._total_requests = 0
        self._successful_requests = 0
        self._failed_requests = 0
        self._proxy_requests = 0
        self._rate_limit_errors = 0
        self._retry_attempts = 0
        
        # Время запуска для uptime
        self._start_time = time.time()
        
        log_admin(f"[PerformanceMonitor] Initialized with window_size={self._window_size}, enabled={self._enabled}", level="info")
    
    def is_enabled(self) -> bool:
        """Проверяет, включен ли мониторинг."""
        return self._enabled
    
    def record_request(self, 
                      duration: float,
                      success: bool,
                      api_key: str,
                      model: str = "unknown",
                      error_type: Optional[str] = None,
                      proxy_used: bool = False,
                      retry_count: int = 0) -> None:
        """
        Записывает метрики запроса.
        
        Args:
            duration: Время выполнения запроса в секундах
            success: Успешность запроса
            api_key: API ключ (будет сокращен до последних 4 символов)
            model: Название модели
            error_type: Тип ошибки (если есть)
            proxy_used: Использовался ли прокси
            retry_count: Количество повторных попыток
        """
        if not self._enabled:
            return
        
        with self._lock:
            # Создаем метрику запроса
            api_key_suffix = api_key[-4:] if len(api_key) >= 4 else "****"
            metric = RequestMetrics(
                timestamp=time.time(),
                duration=duration,
                success=success,
                api_key_suffix=api_key_suffix,
                model=model,
                error_type=error_type,
                proxy_used=proxy_used,
                retry_count=retry_count
            )
            
            # Добавляем в историю
            self._request_history.append(metric)
            self._api_key_metrics[api_key_suffix].append(metric)
            self._model_metrics[model].append(metric)
            
            # Обновляем счетчики
            self._total_requests += 1
            if success:
                self._successful_requests += 1
            else:
                self._failed_requests += 1
            
            if proxy_used:
                self._proxy_requests += 1
            
            if error_type == "rate_limit":
                self._rate_limit_errors += 1
            
            self._retry_attempts += retry_count
            
            # Логируем медленные запросы
            if duration > self._slow_threshold:
                log_admin(f"[PerformanceMonitor] Slow request detected: {duration:.2f}s for {model} with key ...{api_key_suffix}", level="warning")
    
    def get_current_metrics(self) -> AggregatedMetrics:
        """Получает текущие агрегированные метрики."""
        if not self._enabled:
            return AggregatedMetrics()
        
        with self._lock:
            if not self._request_history:
                return AggregatedMetrics()
            
            # Собираем данные из истории
            durations = [m.duration for m in self._request_history]
            successful = sum(1 for m in self._request_history if m.success)
            slow_requests = sum(1 for m in self._request_history if m.duration > self._slow_threshold)
            proxy_requests = sum(1 for m in self._request_history if m.proxy_used)
            rate_limit_errors = sum(1 for m in self._request_history if m.error_type == "rate_limit")
            retry_attempts = sum(m.retry_count for m in self._request_history)
            
            total = len(self._request_history)
            
            return AggregatedMetrics(
                total_requests=total,
                successful_requests=successful,
                failed_requests=total - successful,
                avg_response_time=statistics.mean(durations) if durations else 0.0,
                min_response_time=min(durations) if durations else 0.0,
                max_response_time=max(durations) if durations else 0.0,
                success_rate=(successful / total * 100) if total > 0 else 0.0,
                slow_requests=slow_requests,
                proxy_requests=proxy_requests,
                rate_limit_errors=rate_limit_errors,
                retry_attempts=retry_attempts
            )
    
    def get_api_key_metrics(self, api_key_suffix: str) -> Optional[AggregatedMetrics]:
        """Получает метрики для конкретного API ключа."""
        if not self._enabled:
            return None
        
        with self._lock:
            if api_key_suffix not in self._api_key_metrics:
                return None
            
            metrics = list(self._api_key_metrics[api_key_suffix])
            if not metrics:
                return None
            
            durations = [m.duration for m in metrics]
            successful = sum(1 for m in metrics if m.success)
            slow_requests = sum(1 for m in metrics if m.duration > self._slow_threshold)
            proxy_requests = sum(1 for m in metrics if m.proxy_used)
            rate_limit_errors = sum(1 for m in metrics if m.error_type == "rate_limit")
            retry_attempts = sum(m.retry_count for m in metrics)
            
            total = len(metrics)
            
            return AggregatedMetrics(
                total_requests=total,
                successful_requests=successful,
                failed_requests=total - successful,
                avg_response_time=statistics.mean(durations),
                min_response_time=min(durations),
                max_response_time=max(durations),
                success_rate=(successful / total * 100),
                slow_requests=slow_requests,
                proxy_requests=proxy_requests,
                rate_limit_errors=rate_limit_errors,
                retry_attempts=retry_attempts
            )
    
    def get_model_metrics(self, model: str) -> Optional[AggregatedMetrics]:
        """Получает метрики для конкретной модели."""
        if not self._enabled:
            return None
        
        with self._lock:
            if model not in self._model_metrics:
                return None
            
            metrics = list(self._model_metrics[model])
            if not metrics:
                return None
            
            durations = [m.duration for m in metrics]
            successful = sum(1 for m in metrics if m.success)
            slow_requests = sum(1 for m in metrics if m.duration > self._slow_threshold)
            proxy_requests = sum(1 for m in metrics if m.proxy_used)
            rate_limit_errors = sum(1 for m in metrics if m.error_type == "rate_limit")
            retry_attempts = sum(m.retry_count for m in metrics)
            
            total = len(metrics)
            
            return AggregatedMetrics(
                total_requests=total,
                successful_requests=successful,
                failed_requests=total - successful,
                avg_response_time=statistics.mean(durations),
                min_response_time=min(durations),
                max_response_time=max(durations),
                success_rate=(successful / total * 100),
                slow_requests=slow_requests,
                proxy_requests=proxy_requests,
                rate_limit_errors=rate_limit_errors,
                retry_attempts=retry_attempts
            )
    
    def get_uptime(self) -> float:
        """Получает время работы монитора в секундах."""
        return time.time() - self._start_time
    
    def get_summary_report(self) -> Dict[str, Any]:
        """Получает сводный отчет о производительности."""
        if not self._enabled:
            return {"enabled": False}
        
        current_metrics = self.get_current_metrics()
        uptime = self.get_uptime()
        
        # Топ API ключей по количеству запросов
        with self._lock:
            api_key_stats = {}
            for key_suffix, metrics in self._api_key_metrics.items():
                if metrics:
                    api_key_stats[key_suffix] = len(metrics)
        
        # Топ моделей по количеству запросов
        model_stats = {}
        with self._lock:
            for model, metrics in self._model_metrics.items():
                if metrics:
                    model_stats[model] = len(metrics)
        
        return {
            "enabled": True,
            "uptime_seconds": uptime,
            "uptime_formatted": str(timedelta(seconds=int(uptime))),
            "current_metrics": current_metrics,
            "top_api_keys": dict(sorted(api_key_stats.items(), key=lambda x: x[1], reverse=True)[:5]),
            "top_models": dict(sorted(model_stats.items(), key=lambda x: x[1], reverse=True)[:5]),
            "window_size": self._window_size,
            "slow_threshold": self._slow_threshold
        }
    
    def reset_metrics(self) -> None:
        """Сбрасывает все метрики."""
        if not self._enabled:
            return
        
        with self._lock:
            self._request_history.clear()
            self._api_key_metrics.clear()
            self._model_metrics.clear()
            
            self._total_requests = 0
            self._successful_requests = 0
            self._failed_requests = 0
            self._proxy_requests = 0
            self._rate_limit_errors = 0
            self._retry_attempts = 0
            
            self._start_time = time.time()
            
            log_admin("[PerformanceMonitor] All metrics reset", level="info")


# Глобальный экземпляр монитора производительности
performance_monitor = PerformanceMonitor()
