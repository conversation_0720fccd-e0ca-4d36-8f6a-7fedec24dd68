"""
Система управления статусами создания подкастов.
Обеспечивает унифицированное отображение прогресса для всех типов подкастов.
Версия 2.0: с равномерным заполнением прогресс-бара по времени.
"""

import threading
import time
from datetime import datetime
from typing import Dict, List, Optional
from telebot import TeleBot
from telebot import types

# Импортируем логгер и конфигурацию
from bot_globals import log_admin
from config import PODCAST_STAGES_CONFIG, DIANA_SASHA_PODCAST_STATUSES
import uuid # Для уникального ключа процесса

# Глобальный реестр активных менеджеров статусов для предотвращения дублирования
_active_status_managers = {}

class PodcastStatusManager:
    """
    Менеджер для управления статусами создания подкастов.
    Версия 2.0: Обеспечивает плавное заполнение прогресс-бара,
    основанное на времени, а не на этапах.
    """

    def __init__(self, bot: TeleBot, chat_id: int, message_id: int, podcast_type: str, process_params: Dict = None, process_key: str = None, duration_minutes: int = 10):
        """
        Инициализация менеджера статусов.

        Args:
            duration_minutes: Длительность подкаста в минутах (3, 5 или 10). По умолчанию 10.
        """
        # Проверяем, есть ли уже активный менеджер для этого сообщения
        message_key = f"{chat_id}_{message_id}"
        if message_key in _active_status_managers:
            existing_manager = _active_status_managers[message_key]
            log_admin(f"Warning: Status manager already exists for chat {chat_id}, msg {message_id}. Stopping existing manager.", level="warning")
            try:
                existing_manager.complete()
            except Exception as e:
                log_admin(f"Error stopping existing manager: {e}", level="warning")

        self.bot = bot
        self.chat_id = chat_id
        self.message_id = message_id
        self.podcast_type = podcast_type
        self.process_key = process_key or str(uuid.uuid4())
        self.process_params = process_params or {}
        self.duration_minutes = duration_minutes

        # Состояние процесса
        self.current_stage_index = 0
        self.current_stage_text = ""
        self.start_time = datetime.now()
        self.is_cancelled = False
        self.is_completed = False

        # Динамические параметры для равномерного прогресса
        self.total_duration = 7 * 60  # Длительность в секундах (7 минут)
        self.update_interval = max(int(self.total_duration / 25), 30)  # не чаще 1 раза в 30 с
        self.update_timer: Optional[threading.Timer] = None

        # Для предотвращения дублирования сообщений
        self.last_message_text = ""

        # Получаем конфигурацию этапов для данного типа подкаста
        self.stages = PODCAST_STAGES_CONFIG.get(podcast_type, PODCAST_STAGES_CONFIG['regular'])

        # Регистрируем этот менеджер в глобальном реестре
        _active_status_managers[message_key] = self

        self._register_process()

    def extend_total_duration(self, extra_seconds: int):
        """Продлевает общую длительность подкаста на указанное количество секунд."""
        self.total_duration += extra_seconds

    def _calculate_stage_durations(self) -> List[int]:
        """
        Рассчитывает длительности этапов пропорционально общей длительности подкаста.

        Базовые длительности для 10 минут: [120, 120, 180, 120, 60] = 600 сек
        Масштабируются пропорционально для других длительностей.

        Returns:
            List[int]: Список длительностей этапов в секундах
        """
        # Базовые длительности этапов для 10-минутного подкаста
        base_durations = [120, 120, 180, 120, 60]  # Сумма = 600 секунд
        base_total = sum(base_durations)

        # Масштабируем пропорционально
        scale_factor = self.total_duration / base_total
        scaled_durations = [int(duration * scale_factor) for duration in base_durations]

        return scaled_durations

    def _register_process(self):
        import processing_core
        if not hasattr(processing_core, 'active_podcast_processes'):
            processing_core.active_podcast_processes = {}
        processing_core.active_podcast_processes[self.process_key] = {
            'manager': self,
            'chat_id': self.chat_id,
            'message_id': self.message_id,
            'podcast_type': self.podcast_type,
            'start_time': self.start_time,
            'process_params': self.process_params,
            'user_id': self.process_params.get('user_id')  # Add user_id from process_params
        }

        # Регистрация процесса (без queue_manager)
        log_admin(f"[{self.process_key}] Registered podcast process (type: {self.podcast_type}, chat: {self.chat_id})")

    def _unregister_process(self):
        import processing_core
        if hasattr(processing_core, 'active_podcast_processes'):
            processing_core.active_podcast_processes.pop(self.process_key, None)

        # Отмена регистрации процесса (без queue_manager)
        log_admin(f"[{self.process_key}] Unregistered podcast process (chat: {self.chat_id})")

    def generate_progress_bar(self, percentage: int, length: int = 10) -> str:
        # Минимальное заполнение при любом проценте больше 0
        if percentage > 0:
            filled = max(1, int(length * percentage / 100))
        else:
            filled = 0
        empty = length - filled
        return "▓" * filled + "░" * empty

    def estimate_time_remaining(self) -> str:
        elapsed = (datetime.now() - self.start_time).total_seconds()
        remaining = max(0, self.total_duration - elapsed)

        if self.is_completed:
             return "Готово!"
        if remaining < 30:
            return "Почти готово!"
        elif remaining < 90:
            return "Осталось ~1 минута"
        else:
            minutes = int(remaining / 60)
            if minutes == 1:
                return "Осталось ~1 минута"
            else:
                return f"Осталось ~{minutes} минут"

    def format_status_message(self) -> str:
        """
        Форматирует сообщение о статусе на основе прошедшего времени.
        """
        # 1. Рассчитываем процент выполнения на основе времени
        elapsed = (datetime.now() - self.start_time).total_seconds()
        percentage = min(100, int(elapsed / self.total_duration * 100))

        # 2. Генерируем прогресс-бар и оценку времени
        progress_bar = self.generate_progress_bar(percentage)
        time_remaining = self.estimate_time_remaining()

        # 3. Получаем заголовок
        title_map = {
            'regular': "🎙️ Создание подкаста",
            'thematic': "🎙️ Создание тематического подкаста",
            'research': "🎙️ Создание подкаста исследования",
            'diana_sasha': "🎙️ Создание подкаста исследования"
        }
        title = title_map.get(self.podcast_type, "🎙️ Создание подкаста")

        # 4. Формируем итоговое сообщение
        return f"""<b>{title}</b>

{self.current_stage_text}
{progress_bar} {percentage}%
⏱️ {time_remaining}"""

    def _update_message(self):
        """Внутренний метод для редактирования сообщения в Telegram."""
        # Проверяем состояние в самом начале
        if self.is_cancelled or self.is_completed:
            self.stop_auto_update()
            return

        try:
            message_text = self.format_status_message()

            # Проверяем, изменилось ли содержимое сообщения
            if message_text == self.last_message_text:
                # Сообщение не изменилось, пропускаем обновление
                pass
            else:
                # Кнопки отмены процесса отключены (всегда None)
                cancel_markup = self.create_cancel_button()

                self.bot.edit_message_text(
                    text=message_text,
                    chat_id=self.chat_id,
                    message_id=self.message_id,
                    parse_mode="HTML",
                    reply_markup=cancel_markup
                )

                # Сохраняем текст последнего сообщения
                self.last_message_text = message_text

        except Exception as e:
            # Если сообщение не найдено, останавливаем таймер
            if "message to edit not found" in str(e):
                log_admin(f"Status message {self.message_id} not found. Stopping updates.", level="warning")
                self.stop_auto_update()
                return
            elif "message is not modified" in str(e):
                # Это нормальная ситуация, когда содержимое не изменилось
                # Просто игнорируем эту ошибку
                pass
            else:
                log_admin(f"Error updating podcast status message: {e}", level="error")

        # Проверяем состояние еще раз перед перезапуском таймера
        if not self.is_cancelled and not self.is_completed:
            self.update_timer = threading.Timer(self.update_interval, self._update_message)
            self.update_timer.start()
        else:
            # Если состояние изменилось во время выполнения, останавливаем обновления
            self.stop_auto_update()

    def start(self):
        """Запускает процесс обновления статуса."""
        log_admin(f"[{self.process_key}] Starting status manager for chat {self.chat_id}, msg {self.message_id}", level="info")
        self.set_stage(0) # Устанавливаем начальный этап

    def set_stage(self, stage_index: int):
        """Обновляет текстовое описание этапа."""
        if self.is_cancelled or self.is_completed:
            return

        if stage_index < len(self.stages):
            self.current_stage_index = stage_index
            stage_info = self.stages[stage_index]
            self.current_stage_text = f"{stage_info['icon']} {stage_info['message']}"

            # Сбрасываем кеш последнего сообщения при изменении этапа
            self.last_message_text = ""

            # Если таймер еще не запущен, запускаем его
            if self.update_timer is None:
                 self._update_message() # Первый вызов для немедленного обновления, он сам запустит таймер

    def start_auto_update(self, stage_index: int, custom_messages=None):
        """Устанавливает этап и запускает автоматическое обновление статуса."""
        if custom_messages:
            # Если переданы кастомные сообщения, используем их
            self.stages = custom_messages

        self.set_stage(stage_index)

    def stop_auto_update(self):
        """Останавливает автоматическое обновление статуса."""
        if self.update_timer:
            try:
                self.update_timer.cancel()
            except Exception as e:
                log_admin(f"Error cancelling timer: {e}", level="warning")
            finally:
                self.update_timer = None
                log_admin(f"Stopped status manager for chat {self.chat_id}, msg {self.message_id}", level="info")

    def complete(self):
        """Завершает процесс, останавливает таймер и удаляет статусное сообщение."""
        self.is_completed = True
        self.stop_auto_update()
        self._unregister_process()

        # Удаляем из глобального реестра
        message_key = f"{self.chat_id}_{self.message_id}"
        if message_key in _active_status_managers:
            del _active_status_managers[message_key]

        # Дополнительная проверка - если таймер все еще активен, принудительно останавливаем
        if self.update_timer:
            try:
                self.update_timer.cancel()
                self.update_timer = None
                log_admin(f"[{self.process_key}] Force stopped remaining timer for chat {self.chat_id}, msg {self.message_id}", level="warning")
            except Exception as e:
                log_admin(f"Error force stopping timer: {e}", level="warning")

        # Удаляем статусное сообщение с помощью безопасной функции
        try:
            from utils import safe_delete_message
            safe_delete_message(self.bot, self.chat_id, self.message_id)
            log_admin(f"[{self.process_key}] Status message deleted successfully")
        except Exception as e:
            log_admin(f"[{self.process_key}] Error importing or using safe_delete_message: {e}", level="error")

    def cancel_process(self):
        """Отменяет процесс создания подкаста."""
        self.is_cancelled = True
        self.stop_auto_update()

        # Отмена подкаста (без queue_manager)
        log_admin(f"[{self.process_key}] Cancelled podcast process (chat: {self.chat_id})")

        self._unregister_process()

        # Удаляем из глобального реестра
        message_key = f"{self.chat_id}_{self.message_id}"
        if message_key in _active_status_managers:
            del _active_status_managers[message_key]

        # Показываем сообщение об отмене
        try:
            self.bot.edit_message_text(
                text="❌ Создание подкаста отменено пользователем.",
                chat_id=self.chat_id,
                message_id=self.message_id,
                parse_mode="HTML"
            )
        except Exception as e:
            log_admin(f"Error showing cancellation message: {e}", level="error")

    def show_error(self, error_type: str, error_details: str = ""):
        """Показывает сообщение об ошибке и останавливает таймер."""
        self.is_cancelled = True # Считаем ошибку отменой
        self.stop_auto_update()

        # НЕ удаляем процесс из active_podcast_processes - он нужен для кнопки "Повторить"
        # Вместо этого помечаем процесс как ошибочный
        import processing_core
        if hasattr(processing_core, 'active_podcast_processes') and self.process_key in processing_core.active_podcast_processes:
            processing_core.active_podcast_processes[self.process_key]['is_error'] = True

        # Удаляем из глобального реестра
        message_key = f"{self.chat_id}_{self.message_id}"
        if message_key in _active_status_managers:
            del _active_status_managers[message_key]

        markup = types.InlineKeyboardMarkup()
        # Логика кнопок для ошибок
        retry_btn = types.InlineKeyboardButton("🔄 Повторить", callback_data=f"retry_podcast_process_{self.process_key}")
        close_btn = types.InlineKeyboardButton("❌ Закрыть", callback_data=f"close_podcast_message_{self.process_key}")
        markup.add(retry_btn, close_btn)

        # Определяем правильный заголовок ошибки
        error_titles = {
            'censorship': "❌ <b>Ошибка цензуры</b>",
            'tts_rejected': "❌ <b>Ошибка синтеза речи</b>",
            'general': "❌ <b>Ошибка</b>",
            'api_error': "❌ <b>Ошибка API</b>",
            'timeout': "❌ <b>Превышено время ожидания</b>"
        }

        error_title = error_titles.get(error_type, "❌ <b>Ошибка</b>")
        error_message = f"{error_title}\n\n{error_details}"

        try:
            self.bot.edit_message_text(
                text=error_message,
                chat_id=self.chat_id,
                message_id=self.message_id,
                parse_mode="HTML",
                reply_markup=markup
            )
        except Exception as e:
            if "message is not modified" not in str(e):
                log_admin(f"Error showing error status: {e}", level="error")

    def create_cancel_button(self) -> Optional[types.InlineKeyboardMarkup]:
        # Кнопки отмены процесса отключены по требованию пользователя
        # Ранее кнопка отмены была доступна только на первых двух этапах (сбор и анализ)
        # Теперь кнопки отмены процесса полностью убраны из статусных сообщений
        return None