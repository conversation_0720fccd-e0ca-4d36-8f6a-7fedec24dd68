"""
Модуль управления ресурсами для Gemini API.
Обеспечивает эффективное использование соединений, управление памятью и автоматическую очистку.
"""

import time
import threading
import weakref
from typing import Dict, Set, Optional, Any, Tuple, List
from dataclasses import dataclass
from datetime import datetime, timedelta
import gc

try:
    from bot_globals import log_admin
except ImportError:
    def log_admin(message, level="info"):
        print(f"[{level.upper()}] {message}")

import config


@dataclass
class ClientInfo:
    """Информация о клиенте для отслеживания использования."""
    client_id: str
    api_key_suffix: str
    created_at: float
    last_used: float
    usage_count: int
    is_proxy: bool
    is_async: bool


class ResourceManager:
    """
    Менеджер ресурсов для эффективного управления клиентами Gemini API.
    Обеспечивает connection pooling, автоматическую очистку и мониторинг использования.
    """
    
    def __init__(self):
        """Инициализация менеджера ресурсов."""
        # Настройки из конфигурации
        self._cache_ttl = getattr(config, 'GENAI_CLIENT_CACHE_TTL', 3600)  # 1 час
        self._cleanup_interval = 300  # 5 минут (реже очистка для большего объема)
        self._max_concurrent = getattr(config, 'GENAI_MAX_CONCURRENT_REQUESTS', 50)
        self._max_clients_in_registry = 500  # Максимальное количество клиентов в registry (увеличено)
        
        # Thread-safe структуры данных
        self._lock = threading.RLock()
        self._client_registry: Dict[str, ClientInfo] = {}
        self._active_requests: Set[str] = set()  # Активные запросы по client_id
        self._cleanup_timer: Optional[threading.Timer] = None
        
        # Статистика
        self._total_clients_created = 0
        self._total_clients_cleaned = 0
        self._peak_concurrent_requests = 0
        
        # Запускаем периодическую очистку
        self._start_cleanup_timer()
        
        log_admin(f"[ResourceManager] Initialized with cache_ttl={self._cache_ttl}s, cleanup_interval={self._cleanup_interval}s", level="info")
    
    def register_client(self, client_id: str, api_key: str, is_proxy: bool = False, is_async: bool = False) -> None:
        """
        Регистрирует клиента в менеджере ресурсов.
        
        Args:
            client_id: Уникальный идентификатор клиента
            api_key: API ключ
            is_proxy: Является ли клиент прокси-клиентом
            is_async: Является ли клиент асинхронным
        """
        with self._lock:
            # Проверяем лимит клиентов в registry
            if len(self._client_registry) >= self._max_clients_in_registry:
                # Принудительно очищаем старые клиенты
                cleaned = self.cleanup_expired_clients(force=False)
                if cleaned == 0 and len(self._client_registry) >= self._max_clients_in_registry:
                    # Если не удалось очистить, удаляем самые старые
                    oldest_clients = sorted(self._client_registry.items(),
                                          key=lambda x: x[1].last_used)[:10]
                    for client_id, _ in oldest_clients:
                        del self._client_registry[client_id]
                        self._active_requests.discard(client_id)
                    log_admin(f"[ResourceManager] Forced cleanup of {len(oldest_clients)} oldest clients", level="warning")

            api_key_suffix = api_key[-4:] if len(api_key) >= 4 else "****"
            current_time = time.time()

            client_info = ClientInfo(
                client_id=client_id,
                api_key_suffix=api_key_suffix,
                created_at=current_time,
                last_used=current_time,
                usage_count=0,
                is_proxy=is_proxy,
                is_async=is_async
            )
            
            self._client_registry[client_id] = client_info
            self._total_clients_created += 1
            
            log_admin(f"[ResourceManager] Registered client {client_id} for key ...{api_key_suffix} (proxy={is_proxy}, async={is_async})", level="debug")
    
    def update_client_usage(self, client_id: str) -> None:
        """
        Обновляет информацию об использовании клиента.
        
        Args:
            client_id: Идентификатор клиента
        """
        with self._lock:
            if client_id in self._client_registry:
                client_info = self._client_registry[client_id]
                client_info.last_used = time.time()
                client_info.usage_count += 1
                
                log_admin(f"[ResourceManager] Updated usage for client {client_id} (count={client_info.usage_count})", level="debug")
    
    def start_request(self, client_id: str) -> bool:
        """
        Начинает отслеживание активного запроса.
        
        Args:
            client_id: Идентификатор клиента
            
        Returns:
            True если запрос может быть начат, False если превышен лимит
        """
        with self._lock:
            # Проверяем лимит одновременных запросов
            if len(self._active_requests) >= self._max_concurrent:
                log_admin(f"[ResourceManager] Concurrent request limit reached ({self._max_concurrent})", level="warning")
                return False
            
            self._active_requests.add(client_id)
            
            # Обновляем пиковое значение
            current_concurrent = len(self._active_requests)
            if current_concurrent > self._peak_concurrent_requests:
                self._peak_concurrent_requests = current_concurrent
            
            log_admin(f"[ResourceManager] Started request for client {client_id} (active={current_concurrent})", level="debug")
            return True
    
    def end_request(self, client_id: str) -> None:
        """
        Завершает отслеживание активного запроса.
        
        Args:
            client_id: Идентификатор клиента
        """
        with self._lock:
            self._active_requests.discard(client_id)
            self.update_client_usage(client_id)
            
            log_admin(f"[ResourceManager] Ended request for client {client_id} (active={len(self._active_requests)})", level="debug")
    
    def is_client_expired(self, client_id: str) -> bool:
        """
        Проверяет, истек ли срок жизни клиента.
        
        Args:
            client_id: Идентификатор клиента
            
        Returns:
            True если клиент истек
        """
        with self._lock:
            if client_id not in self._client_registry:
                return True
            
            client_info = self._client_registry[client_id]
            current_time = time.time()
            
            # Проверяем TTL
            if (current_time - client_info.last_used) > self._cache_ttl:
                return True
            
            return False
    
    def cleanup_expired_clients(self, force: bool = False) -> int:
        """
        Очищает истекшие клиенты.
        
        Args:
            force: Принудительная очистка всех клиентов
            
        Returns:
            Количество очищенных клиентов
        """
        with self._lock:
            current_time = time.time()
            expired_clients = []
            
            for client_id, client_info in self._client_registry.items():
                # Не удаляем активные клиенты
                if client_id in self._active_requests and not force:
                    continue
                
                # Проверяем TTL или принудительную очистку
                if force or (current_time - client_info.last_used) > self._cache_ttl:
                    expired_clients.append(client_id)
            
            # Удаляем истекшие клиенты
            for client_id in expired_clients:
                del self._client_registry[client_id]
                self._active_requests.discard(client_id)
                self._total_clients_cleaned += 1
            
            if expired_clients:
                log_admin(f"[ResourceManager] Cleaned up {len(expired_clients)} expired clients", level="info")
                
                # Принудительная сборка мусора после очистки
                gc.collect()
            
            return len(expired_clients)
    
    def get_client_stats(self) -> Dict[str, Any]:
        """Получает статистику клиентов."""
        with self._lock:
            current_time = time.time()
            
            # Группируем клиентов по типам
            normal_clients = 0
            proxy_clients = 0
            async_clients = 0
            async_proxy_clients = 0
            
            for client_info in self._client_registry.values():
                if client_info.is_async and client_info.is_proxy:
                    async_proxy_clients += 1
                elif client_info.is_async:
                    async_clients += 1
                elif client_info.is_proxy:
                    proxy_clients += 1
                else:
                    normal_clients += 1
            
            # Статистика по API ключам
            api_key_usage = {}
            for client_info in self._client_registry.values():
                key = client_info.api_key_suffix
                if key not in api_key_usage:
                    api_key_usage[key] = {"count": 0, "total_usage": 0}
                api_key_usage[key]["count"] += 1
                api_key_usage[key]["total_usage"] += client_info.usage_count
            
            return {
                "total_registered": len(self._client_registry),
                "normal_clients": normal_clients,
                "proxy_clients": proxy_clients,
                "async_clients": async_clients,
                "async_proxy_clients": async_proxy_clients,
                "active_requests": len(self._active_requests),
                "peak_concurrent_requests": self._peak_concurrent_requests,
                "total_created": self._total_clients_created,
                "total_cleaned": self._total_clients_cleaned,
                "api_key_usage": api_key_usage,
                "cache_ttl": self._cache_ttl,
                "max_concurrent": self._max_concurrent
            }
    
    def get_resource_health(self) -> Dict[str, Any]:
        """Получает информацию о здоровье ресурсов."""
        with self._lock:
            current_time = time.time()
            
            # Анализируем состояние клиентов
            healthy_clients = 0
            expiring_soon = 0  # Истекают в течение 10% от TTL
            expired_clients = 0
            
            expiring_threshold = self._cache_ttl * 0.1  # 10% от TTL
            
            for client_info in self._client_registry.values():
                time_since_use = current_time - client_info.last_used
                
                if time_since_use > self._cache_ttl:
                    expired_clients += 1
                elif time_since_use > (self._cache_ttl - expiring_threshold):
                    expiring_soon += 1
                else:
                    healthy_clients += 1
            
            # Вычисляем загрузку
            concurrent_load = len(self._active_requests) / self._max_concurrent * 100
            
            # Определяем общее состояние
            if concurrent_load > 90:
                health_status = "critical"
            elif concurrent_load > 70 or expired_clients > 10:
                health_status = "warning"
            else:
                health_status = "healthy"
            
            return {
                "status": health_status,
                "concurrent_load_percent": round(concurrent_load, 2),
                "healthy_clients": healthy_clients,
                "expiring_soon": expiring_soon,
                "expired_clients": expired_clients,
                "memory_pressure": expired_clients > 20,  # Простая эвристика
                "recommendations": self._get_health_recommendations(health_status, concurrent_load, expired_clients)
            }
    
    def _get_health_recommendations(self, status: str, load: float, expired: int) -> List[str]:
        """Получает рекомендации по оптимизации."""
        recommendations = []
        
        if status == "critical":
            recommendations.append("Критическая нагрузка: рассмотрите увеличение лимита одновременных запросов")
        
        if load > 80:
            recommendations.append("Высокая нагрузка: рекомендуется мониторинг производительности")
        
        if expired > 20:
            recommendations.append("Много истекших клиентов: рекомендуется принудительная очистка")
        
        if expired > 50:
            recommendations.append("Критическое количество истекших клиентов: возможна утечка памяти")
        
        if not recommendations:
            recommendations.append("Система работает в нормальном режиме")
        
        return recommendations
    
    def _start_cleanup_timer(self) -> None:
        """Запускает таймер периодической очистки."""
        def cleanup_task():
            try:
                self.cleanup_expired_clients()
            except Exception as e:
                log_admin(f"[ResourceManager] Error during cleanup: {e}", level="error")
            finally:
                # Перезапускаем таймер
                self._start_cleanup_timer()
        
        self._cleanup_timer = threading.Timer(self._cleanup_interval, cleanup_task)
        self._cleanup_timer.daemon = True
        self._cleanup_timer.start()
    
    def shutdown(self) -> None:
        """Завершает работу менеджера ресурсов."""
        with self._lock:
            if self._cleanup_timer:
                self._cleanup_timer.cancel()
                self._cleanup_timer = None
            
            # Принудительная очистка всех клиентов
            cleaned = self.cleanup_expired_clients(force=True)
            
            log_admin(f"[ResourceManager] Shutdown completed, cleaned {cleaned} clients", level="info")


# Глобальный экземпляр менеджера ресурсов
resource_manager = ResourceManager()
