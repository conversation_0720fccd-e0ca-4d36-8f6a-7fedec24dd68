"""
Конфигурация сервера для высокопроизводительной работы.
Настройки для сервера с 6GB RAM и 80% CPU (4 ядра).
"""

# Пороги ресурсов для высокопроизводительного сервера (6GB RAM)
SERVER_CONFIG = {
    # Основные характеристики сервера
    'server_type': 'high_performance',  # low_resource, medium, high_performance
    'total_ram_mb': 6144,  # 6GB RAM
    'cpu_cores': 4,  # Предполагаем 4 ядра
    'max_cpu_percent': 80,  # Используем больше CPU
    
    # Пороги для проверки ресурсов (оптимизированы для стабильности)
    'resource_thresholds': {
        'cpu_critical': 98,      # Менее агрессивная проверка CPU (было 95%)
        'cpu_warning': 85,       # Предупреждение CPU
        'memory_critical': 92,   # Менее агрессивная проверка памяти (было 90%)
        'memory_warning': 80,    # Предупреждение памяти
        'disk_critical_mb': 50,  # Критический уровень диска в MB
        'disk_warning_mb': 200,  # Предупреждение диска в MB
    },
    
    # Настройки FFmpeg - оптимизированы для высокопроизводительного сервера
    'ffmpeg_settings': {
        'max_threads': 4,           # Используем больше потоков для быстрой обработки
        'preset': 'medium',         # Лучшее качество при достаточных ресурсах
        'audio_bitrate': '128k',    # Хорошее качество (восстановлено)
        'sample_rate': 44100,       # Стандартное качество (восстановлено)
        'channels': 1,              # Моно (для подкастов достаточно)
        'timeout_seconds': 300,     # Таймаут операций
        'memory_limit': '1G',       # Увеличенное ограничение памяти для FFmpeg
        'nice_priority': 10,        # Низкий приоритет процесса
    },
    
    # Настройки очереди операций
    'queue_settings': {
        'max_concurrent_operations': 8,  # Увеличено для высокопроизводительного сервера
        'queue_timeout_seconds': 30,     # Таймаут ожидания в очереди
        'retry_attempts': 5,             # Количество попыток
        'retry_delay_seconds': 1,        # Задержка между попытками (оптимизировано)
    },
    
    # Настройки очистки
    'cleanup_settings': {
        'auto_cleanup_enabled': True,
        'cleanup_age_hours': 1,          # Удалять файлы старше 1 часа
        'min_free_space_mb': 200,        # Минимум свободного места
        'cleanup_patterns': [
            'podcast_*.mp3',
            'diana_sasha_podcast_*.mp3',
            'silence_*.mp3',
            '*_part1.mp3',
            '*_part2.mp3',
            'test_*.mp3',
            '*_combined_*.mp3'
        ]
    },
    
    # Настройки мониторинга
    'monitoring': {
        'health_check_interval': 60,     # Проверка каждую минуту
        'log_resource_usage': True,      # Логировать использование ресурсов
        'alert_on_critical': True,       # Уведомления при критическом состоянии
    }
}

# Настройки для разных типов серверов
SERVER_PROFILES = {
    'low_resource': {
        'description': '512MB RAM, 1 CPU core, limited resources - quality preserved',
        'ffmpeg_threads': 1,
        'ffmpeg_preset': 'fast',        # Компромисс вместо ultrafast
        'audio_bitrate': '128k',        # Восстановлено качество
        'sample_rate': 44100,           # Восстановлено качество
        'cpu_threshold': 95,
        'memory_threshold': 90,
        'use_queue': True,              # Умная очередь для распределения нагрузки
        'aggressive_cleanup': True,
        'process_priority': 'low',      # Низкий приоритет процесса
        'memory_limit': '256M'          # Ограничение памяти
    },
    
    'medium': {
        'description': '1-2GB RAM, 2 CPU cores, moderate resources',
        'ffmpeg_threads': 2,
        'ffmpeg_preset': 'fast',
        'audio_bitrate': '128k',
        'sample_rate': 44100,
        'cpu_threshold': 85,
        'memory_threshold': 85,
        'use_queue': False,
        'aggressive_cleanup': False
    },
    
    'high_performance': {
        'description': '6GB+ RAM, 4+ CPU cores, high resources',
        'ffmpeg_threads': 4,
        'ffmpeg_preset': 'medium',
        'audio_bitrate': '128k',  # Сохраняем 128k для совместимости
        'sample_rate': 44100,
        'cpu_threshold': 80,
        'memory_threshold': 80,
        'use_queue': False,
        'aggressive_cleanup': False,
        'memory_limit': '1G'  # Добавляем ограничение памяти
    }
}

def get_server_config():
    """Получить текущую конфигурацию сервера"""
    return SERVER_CONFIG

def get_server_profile(profile_name=None):
    """Получить профиль сервера"""
    if profile_name is None:
        profile_name = SERVER_CONFIG.get('server_type', 'low_resource')
    
    return SERVER_PROFILES.get(profile_name, SERVER_PROFILES['low_resource'])

def update_server_config(**kwargs):
    """Обновить конфигурацию сервера"""
    for key, value in kwargs.items():
        if key in SERVER_CONFIG:
            SERVER_CONFIG[key] = value

def get_ffmpeg_params():
    """Получить оптимальные параметры FFmpeg для текущего сервера"""
    profile = get_server_profile()
    config = get_server_config()
    
    return {
        'threads': profile['ffmpeg_threads'],
        'preset': profile['ffmpeg_preset'],
        'audio_bitrate': profile['audio_bitrate'],
        'sample_rate': profile['sample_rate'],
        'channels': config['ffmpeg_settings']['channels'],
        'timeout': config['ffmpeg_settings']['timeout_seconds']
    }

def should_use_queue():
    """Определить, следует ли использовать очередь операций"""
    profile = get_server_profile()
    return profile.get('use_queue', True)

def get_resource_thresholds():
    """Получить пороги ресурсов для текущего сервера"""
    profile = get_server_profile()
    return {
        'cpu_critical': profile['cpu_threshold'],
        'memory_critical': profile['memory_threshold'],
        'cpu_warning': profile['cpu_threshold'] - 10,
        'memory_warning': profile['memory_threshold'] - 10
    }

# Автоматическое определение типа сервера на основе доступных ресурсов
def auto_detect_server_type():
    """Автоматически определить тип сервера"""
    try:
        import psutil
        
        # Получаем информацию о системе
        memory = psutil.virtual_memory()
        cpu_count = psutil.cpu_count()
        total_ram_gb = memory.total / (1024**3)
        
        # Определяем тип сервера
        if total_ram_gb < 1 or cpu_count <= 1:
            server_type = 'low_resource'
        elif total_ram_gb < 3 or cpu_count <= 2:
            server_type = 'medium'
        else:
            server_type = 'high_performance'
        
        # Обновляем конфигурацию
        SERVER_CONFIG['server_type'] = server_type
        SERVER_CONFIG['total_ram_mb'] = int(memory.total / (1024**2))
        SERVER_CONFIG['cpu_cores'] = cpu_count
        
        return server_type, total_ram_gb, cpu_count
        
    except Exception as e:
        print(f"Error auto-detecting server type: {e}")
        return 'low_resource', 0.5, 1

# Инициализация при импорте
if __name__ != "__main__":
    auto_detect_server_type()
