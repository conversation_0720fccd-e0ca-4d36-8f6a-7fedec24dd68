"""
Модуль для автоматического сокращения длинных текстов в группах.
Активируется при получении текста длиннее 600 символов в группах.
Использует Cerebras API (qwen-3-235b-a22b) для сокращения текста.
"""

import telebot
from cerebras_client import summarize_text
from bot_globals import log_admin


def check_and_summarize_text(message, bot):
    """
    Проверяет и сокращает длинный текст в группах.
    
    Условия активации:
    - Сообщение в группе (не в приватном чате)
    - Тип контента: текст
    - Длина текста больше 600 символов
    
    Args:
        message: Объект сообщения Telegram
        bot: Объект бота Telegram
    """
    try:
        # Проверяем, что это групповой чат
        if message.chat.type not in ["group", "supergroup"]:
            return
        
        # Проверяем, что это текстовое сообщение
        if message.content_type != "text" or not message.text:
            return
        
        # Проверяем длину текста
        text_length = len(message.text)
        if text_length <= 600:
            return
        
        # Логируем начало обработки
        user_info = f"@{message.from_user.username}" if message.from_user.username else f"ID:{message.from_user.id}"
        chat_info = f"@{message.chat.username}" if message.chat.username else f"ID:{message.chat.id}"
        log_admin(f"[SUMM] Starting text summarization for {user_info} in {chat_info}, text length: {text_length} chars", level="info")
        
        # Сокращаем текст через Cerebras API
        summarized_text = summarize_text(message.text)
        
        if summarized_text:
            # Формируем ответ
            original_length = len(message.text)
            summary_length = len(summarized_text)
            compression_ratio = round((1 - summary_length / original_length) * 100, 1)
            
            response_text = f"📝 **Краткое содержание:**\n\n{summarized_text}"
            
            # Отправляем сокращенный текст
            bot.reply_to(message, response_text, parse_mode='Markdown')
            
            log_admin(f"[SUMM] Text summarized successfully for {user_info} in {chat_info}, compression: {compression_ratio}%", level="info")
        else:
            # Если не удалось сократить текст
            log_admin(f"[SUMM] Failed to summarize text for {user_info} in {chat_info}", level="warning")
            
    except Exception as e:
        # Логируем ошибку, но не прерываем работу бота
        user_info = "unknown"
        chat_info = "unknown"
        try:
            user_info = f"@{message.from_user.username}" if message.from_user.username else f"ID:{message.from_user.id}"
            chat_info = f"@{message.chat.username}" if message.chat.username else f"ID:{message.chat.id}"
        except:
            pass
        
        log_admin(f"[SUMM] Error in check_and_summarize_text for {user_info} in {chat_info}: {e}", level="error")


def get_module_info():
    """
    Возвращает информацию о модуле для диагностики.
    
    Returns:
        dict: Информация о модуле
    """
    return {
        "name": "summ",
        "version": "1.0.0",
        "description": "Автоматическое сокращение длинных текстов в группах",
        "min_text_length": 600,
        "supported_chat_types": ["group", "supergroup"],
        "api_model": "qwen-3-235b-a22b"
    }
