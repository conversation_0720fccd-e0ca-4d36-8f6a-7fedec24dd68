# text_utils.py - Утилиты для работы с текстом
# Отдельный модуль для избежания циклических импортов

from bot_globals import log_admin


def remove_text_duplication(text):
    """
    Удаляет дублирование текста, когда одна и та же фраза повторяется подряд.
    Например: "как делакак дела" -> "как дела", "приветпривет" -> "привет"

    Args:
        text (str): Исходный текст для проверки

    Returns:
        str: Текст без дублирования
    """
    if not text or not isinstance(text, str):
        return text

    # Убираем лишние пробелы в начале и конце
    text = text.strip()

    if len(text) < 2:
        return text

    # Проверяем различные варианты дублирования
    # Начинаем с половины длины текста и идем вниз
    for split_pos in range(len(text) // 2, 0, -1):
        first_part = text[:split_pos]
        second_part = text[split_pos:split_pos * 2]

        # Если вторая часть точно совпадает с первой
        if first_part == second_part:
            # Проверяем, что после дублированной части нет еще текста
            remaining = text[split_pos * 2:]
            if not remaining.strip():
                log_admin(f"[TextDeduplication] Removed duplication: '{text}' -> '{first_part}'", level="debug")
                return first_part
            else:
                # Если есть остаток, возвращаем первую часть + остаток
                result = first_part + remaining
                log_admin(f"[TextDeduplication] Removed duplication with remainder: '{text}' -> '{result}'", level="debug")
                return result

    # Если дублирование не найдено, возвращаем исходный текст
    return text
